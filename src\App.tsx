import React, { useState } from 'react';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import WelcomeScreen from './components/WelcomeScreen';
import UltraModernLoginForm from './components/UltraModernLoginForm';
import RegisterForm from './components/RegisterForm';
import ModernClientDashboard from './components/ModernClientDashboard';
import ModernAdminDashboard from './components/ModernAdminDashboard';
import PWAInstallPrompt from './components/PWAInstallPrompt';

type AppScreen = 'welcome' | 'login' | 'register';

const AppContent: React.FC = () => {
  const { isAuthenticated, user, login } = useAuth();
  const [currentScreen, setCurrentScreen] = useState<AppScreen>('welcome');

  const handleRegister = async (userData: any) => {
    // Simulate registration process
    console.log('Registering user:', userData);

    // For demo purposes, automatically log in the user after registration
    // In a real app, you would send this data to your backend
    const newUser = {
      id: Date.now().toString(),
      email: userData.email,
      name: `${userData.firstName} ${userData.lastName}`,
      role: 'client' as const,
      phone: userData.phone,
      address: userData.address,
    };

    login(newUser);
  };

  if (!isAuthenticated) {
    switch (currentScreen) {
      case 'welcome':
        return (
          <WelcomeScreen
            onLogin={() => setCurrentScreen('login')}
            onRegister={() => setCurrentScreen('register')}
          />
        );
      case 'login':
        return (
          <UltraModernLoginForm
            onBack={() => setCurrentScreen('welcome')}
          />
        );
      case 'register':
        return (
          <RegisterForm
            onBack={() => setCurrentScreen('welcome')}
            onRegister={handleRegister}
          />
        );
      default:
        return (
          <WelcomeScreen
            onLogin={() => setCurrentScreen('login')}
            onRegister={() => setCurrentScreen('register')}
          />
        );
    }
  }

  return user?.role === 'admin' ? <ModernAdminDashboard /> : <ModernClientDashboard />;
};

function App() {
  return (
    <AuthProvider>
      <AppContent />
      <PWAInstallPrompt />
    </AuthProvider>
  );
}

export default App;