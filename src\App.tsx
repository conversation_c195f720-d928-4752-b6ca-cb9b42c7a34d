import React, { useState } from 'react';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import WelcomePage from './components/WelcomePage';
import SimpleLoginForm from './components/SimpleLoginForm';
import ModernClientDashboard from './components/ModernClientDashboard';
import ModernAdminDashboard from './components/ModernAdminDashboard';
import PWAInstallPrompt from './components/PWAInstallPrompt';

const AppContent: React.FC = () => {
  const { isAuthenticated, user } = useAuth();
  const [showLogin, setShowLogin] = useState(false);

  if (!isAuthenticated) {
    if (!showLogin) {
      return <WelcomePage onEnter={() => setShowLogin(true)} />;
    }
    return <SimpleLoginForm onBack={() => setShowLogin(false)} />;
  }

  return user?.role === 'admin' ? <ModernAdminDashboard /> : <ModernClientDashboard />;
};

function App() {
  return (
    <AuthProvider>
      <AppContent />
      <PWAInstallPrompt />
    </AuthProvider>
  );
}

export default App;