import React, { useState } from 'react';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import NewAuthFlow from './components/NewAuthFlow';
import ModernClientDashboard from './components/ModernClientDashboard';
import ModernAdminDashboard from './components/ModernAdminDashboard';
import PWAInstallPrompt from './components/PWAInstallPrompt';

const AppContent: React.FC = () => {
  const { isAuthenticated, user } = useAuth();

  if (!isAuthenticated) {
    return <NewAuthFlow />;
  }

  return user?.role === 'admin' ? <ModernAdminDashboard /> : <ModernClientDashboard />;
};

function App() {
  return (
    <AuthProvider>
      <AppContent />
      <PWAInstallPrompt />
    </AuthProvider>
  );
}

export default App;