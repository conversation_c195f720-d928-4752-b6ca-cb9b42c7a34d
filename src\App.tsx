import React from 'react';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import ModernLoginForm from './components/ModernLoginForm';
import ModernClientDashboard from './components/ModernClientDashboard';
import AdminDashboard from './components/AdminDashboard';

const AppContent: React.FC = () => {
  const { isAuthenticated, user } = useAuth();

  if (!isAuthenticated) {
    return <ModernLoginForm />;
  }

  return user?.role === 'admin' ? <AdminDashboard /> : <ModernClientDashboard />;
};

function App() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}

export default App;