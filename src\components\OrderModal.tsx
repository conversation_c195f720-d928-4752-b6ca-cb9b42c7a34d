import React, { useState } from 'react';
import { Product, Order } from '../types';
import { X, CreditCard, Banknote, Smartphone, CheckCircle } from 'lucide-react';

interface OrderModalProps {
  product: Product;
  type: 'buy' | 'reserve';
  onClose: () => void;
  onConfirm: (order: Omit<Order, 'id' | 'date'>) => void;
}

const OrderModal: React.FC<OrderModalProps> = ({ product, type, onClose, onConfirm }) => {
  const [clientName, setClientName] = useState('');
  const [clientPhone, setClientPhone] = useState('');
  const [quantity, setQuantity] = useState(1);
  const [paymentMethod, setPaymentMethod] = useState('card');
  const [showReceipt, setShowReceipt] = useState(false);
  const [orderData, setOrderData] = useState<Omit<Order, 'id' | 'date'> | null>(null);

  const totalPrice = product.price * quantity;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const order: Omit<Order, 'id' | 'date'> = {
      clientName,
      clientPhone,
      product,
      quantity,
      totalPrice,
      status: type === 'buy' ? 'purchased' : 'reserved',
      paymentMethod: paymentMethod === 'card' ? 'Carte bancaire' : paymentMethod === 'cash' ? 'Espèces' : 'Mobile Money'
    };

    setOrderData(order);
    setShowReceipt(true);
    onConfirm(order);
  };

  if (showReceipt && orderData) {
    return (
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
        <div className="bg-white rounded-3xl p-8 max-w-md w-full shadow-2xl border-2 border-orange-200">
          <div className="text-center mb-6">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              {type === 'buy' ? 'Achat confirmé!' : 'Réservation confirmée!'}
            </h2>
          </div>

          <div className="bg-orange-50 rounded-2xl p-6 mb-6 border border-orange-200">
            <h3 className="font-bold text-gray-900 mb-4">Reçu de commande</h3>
            
            <div className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600 font-medium">Client:</span>
                <span className="font-bold">{orderData.clientName}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 font-medium">Téléphone:</span>
                <span className="font-bold">{orderData.clientPhone}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 font-medium">Produit:</span>
                <span className="font-bold">{orderData.product.name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 font-medium">Quantité:</span>
                <span className="font-bold">{orderData.quantity}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 font-medium">Paiement:</span>
                <span className="font-bold">{orderData.paymentMethod}</span>
              </div>
              <div className="border-t border-orange-200 pt-3 flex justify-between font-bold text-lg">
                <span>Total:</span>
                <span className="text-orange-600">{orderData.totalPrice}€</span>
              </div>
            </div>
          </div>

          <button
            onClick={onClose}
            className="w-full bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white py-3 rounded-2xl font-bold transition-all duration-300"
          >
            Fermer
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-3xl p-8 max-w-md w-full shadow-2xl max-h-[90vh] overflow-y-auto border-2 border-orange-200">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-gray-900">
            {type === 'buy' ? 'Acheter' : 'Réserver'} - {product.name}
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-orange-100 rounded-full transition-colors duration-200"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <div className="bg-orange-50 rounded-2xl p-4 mb-6 border border-orange-200">
          <img
            src={product.image}
            alt={product.name}
            className="w-full h-32 object-cover rounded-xl mb-3"
          />
          <div className="text-sm space-y-1">
            <p><span className="font-bold">Taille:</span> {product.size}</p>
            <p><span className="font-bold">Couleur:</span> {product.color}</p>
            <p><span className="font-bold">Prix unitaire:</span> <span className="text-orange-600 font-bold">{product.price}€</span></p>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label className="block text-sm font-bold text-gray-800 mb-2">
              Nom complet
            </label>
            <input
              type="text"
              value={clientName}
              onChange={(e) => setClientName(e.target.value)}
              className="w-full px-4 py-3 border-2 border-orange-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-bold text-gray-800 mb-2">
              Numéro de téléphone
            </label>
            <input
              type="tel"
              value={clientPhone}
              onChange={(e) => setClientPhone(e.target.value)}
              className="w-full px-4 py-3 border-2 border-orange-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-bold text-gray-800 mb-2">
              Quantité
            </label>
            <input
              type="number"
              min="1"
              max={product.stock}
              value={quantity}
              onChange={(e) => setQuantity(parseInt(e.target.value))}
              className="w-full px-4 py-3 border-2 border-orange-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-bold text-gray-800 mb-3">
              Mode de paiement
            </label>
            <div className="grid grid-cols-3 gap-3">
              <button
                type="button"
                onClick={() => setPaymentMethod('card')}
                className={`p-3 rounded-2xl border-2 transition-all duration-200 ${
                  paymentMethod === 'card'
                    ? 'border-orange-500 bg-orange-50 text-orange-700'
                    : 'border-gray-200 hover:border-orange-300'
                }`}
              >
                <CreditCard className="w-6 h-6 mx-auto mb-1" />
                <span className="text-xs font-bold">Carte</span>
              </button>
              <button
                type="button"
                onClick={() => setPaymentMethod('cash')}
                className={`p-3 rounded-2xl border-2 transition-all duration-200 ${
                  paymentMethod === 'cash'
                    ? 'border-green-500 bg-green-50 text-green-700'
                    : 'border-gray-200 hover:border-green-300'
                }`}
              >
                <Banknote className="w-6 h-6 mx-auto mb-1" />
                <span className="text-xs font-bold">Espèces</span>
              </button>
              <button
                type="button"
                onClick={() => setPaymentMethod('mobile')}
                className={`p-3 rounded-2xl border-2 transition-all duration-200 ${
                  paymentMethod === 'mobile'
                    ? 'border-purple-500 bg-purple-50 text-purple-700'
                    : 'border-gray-200 hover:border-purple-300'
                }`}
              >
                <Smartphone className="w-6 h-6 mx-auto mb-1" />
                <span className="text-xs font-bold">Mobile</span>
              </button>
            </div>
          </div>

          <div className="bg-orange-50 rounded-2xl p-4 border border-orange-200">
            <div className="flex justify-between items-center text-lg font-bold">
              <span>Total à payer:</span>
              <span className="text-orange-600">{totalPrice}€</span>
            </div>
          </div>

          <button
            type="submit"
            className={`w-full py-4 rounded-2xl font-bold text-white transition-all duration-300 transform hover:scale-105 shadow-xl ${
              type === 'buy'
                ? 'bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700'
                : 'bg-gradient-to-r from-gray-800 to-black hover:from-gray-900 hover:to-gray-800'
            }`}
          >
            Confirmer {type === 'buy' ? "l'achat" : 'la réservation'}
          </button>
        </form>
      </div>
    </div>
  );
};

export default OrderModal;