import React from 'react';
import { clsx } from 'clsx';

export interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'glass' | 'gradient' | 'elevated';
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  rounded?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl';
  hover?: boolean;
  clickable?: boolean;
}

const Card: React.FC<CardProps> = ({
  children,
  className,
  variant = 'default',
  padding = 'md',
  rounded = '2xl',
  hover = false,
  clickable = false,
  ...props
}) => {
  const baseClasses = [
    'transition-all duration-300',
    'border',
  ];

  const variantClasses = {
    default: [
      'bg-white',
      'border-gray-200',
      'shadow-lg',
    ],
    glass: [
      'glass',
      'border-white/20',
      'backdrop-blur-xl',
    ],
    gradient: [
      'bg-gradient-to-br from-white to-gray-50',
      'border-gray-200',
      'shadow-xl',
    ],
    elevated: [
      'bg-white',
      'border-gray-100',
      'shadow-2xl',
      'shadow-gray-500/10',
    ],
  };

  const paddingClasses = {
    none: '',
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8',
    xl: 'p-10',
  };

  const roundedClasses = {
    sm: 'rounded-lg',
    md: 'rounded-xl',
    lg: 'rounded-2xl',
    xl: 'rounded-3xl',
    '2xl': 'rounded-3xl',
    '3xl': 'rounded-[2rem]',
  };

  const hoverClasses = hover ? [
    'hover:shadow-2xl',
    'hover:scale-105',
    'hover:-translate-y-1',
  ] : [];

  const clickableClasses = clickable ? [
    'cursor-pointer',
    'hover:shadow-2xl',
    'hover:scale-105',
    'active:scale-95',
    'focus:outline-none',
    'focus:ring-2',
    'focus:ring-orange-500',
    'focus:ring-offset-2',
  ] : [];

  const classes = clsx(
    baseClasses,
    variantClasses[variant],
    paddingClasses[padding],
    roundedClasses[rounded],
    hoverClasses,
    clickableClasses,
    className
  );

  return (
    <div
      className={classes}
      {...props}
    >
      {children}
    </div>
  );
};

// Composants spécialisés
export const CardHeader: React.FC<React.HTMLAttributes<HTMLDivElement>> = ({
  children,
  className,
  ...props
}) => (
  <div
    className={clsx('border-b border-gray-200 pb-4 mb-4', className)}
    {...props}
  >
    {children}
  </div>
);

export const CardTitle: React.FC<React.HTMLAttributes<HTMLHeadingElement>> = ({
  children,
  className,
  ...props
}) => (
  <h3
    className={clsx('text-lg font-semibold text-gray-900', className)}
    {...props}
  >
    {children}
  </h3>
);

export const CardDescription: React.FC<React.HTMLAttributes<HTMLParagraphElement>> = ({
  children,
  className,
  ...props
}) => (
  <p
    className={clsx('text-sm text-gray-600 mt-1', className)}
    {...props}
  >
    {children}
  </p>
);

export const CardContent: React.FC<React.HTMLAttributes<HTMLDivElement>> = ({
  children,
  className,
  ...props
}) => (
  <div
    className={clsx('', className)}
    {...props}
  >
    {children}
  </div>
);

export const CardFooter: React.FC<React.HTMLAttributes<HTMLDivElement>> = ({
  children,
  className,
  ...props
}) => (
  <div
    className={clsx('border-t border-gray-200 pt-4 mt-4', className)}
    {...props}
  >
    {children}
  </div>
);

export default Card;
