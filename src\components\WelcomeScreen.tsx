import React from 'react';

interface WelcomeScreenProps {
  onLogin: () => void;
  onRegister: () => void;
}

const WelcomeScreen: React.FC<WelcomeScreenProps> = ({ onLogin, onRegister }) => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-400 via-orange-500 to-orange-600 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-20 left-10 w-32 h-32 bg-white rounded-full blur-xl"></div>
        <div className="absolute top-40 right-20 w-24 h-24 bg-white rounded-full blur-lg"></div>
        <div className="absolute bottom-32 left-1/4 w-40 h-40 bg-white rounded-full blur-2xl"></div>
        <div className="absolute bottom-20 right-10 w-28 h-28 bg-white rounded-full blur-xl"></div>
      </div>

      {/* Floating Elements with CSS animations */}
      <div className="absolute top-1/4 left-8 w-6 h-6 bg-white/20 rounded-full animate-bounce"></div>
      <div className="absolute top-1/3 right-12 w-4 h-4 bg-white/30 rounded-full animate-pulse"></div>
      <div className="absolute bottom-1/3 left-16 w-5 h-5 bg-white/25 rounded-full animate-ping"></div>

      <div className="flex flex-col items-center justify-center min-h-screen px-6 relative z-10">
        {/* 3D Logo Container */}
        <div className="mb-8 animate-fade-in-up">
          <div className="relative">
            {/* 3D Door Logo */}
            <div className="relative w-32 h-40 mx-auto mb-4 transform hover:scale-105 transition-transform duration-300">
              {/* Door Shadow */}
              <div className="absolute top-2 left-2 w-full h-full bg-black/20 rounded-lg transform rotate-3 blur-sm"></div>

              {/* Main Door */}
              <div className="relative w-full h-full bg-gradient-to-br from-amber-100 to-amber-200 rounded-lg shadow-2xl transform -rotate-2 border-4 border-amber-300 hover:shadow-3xl transition-shadow duration-300">
                {/* Door Frame */}
                <div className="absolute inset-2 border-2 border-amber-400 rounded-md"></div>

                {/* Door Handle */}
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <div className="w-3 h-3 bg-gradient-to-br from-yellow-600 to-yellow-800 rounded-full shadow-lg"></div>
                  <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-1.5 h-1.5 bg-yellow-900 rounded-full"></div>
                </div>

                {/* Wood Grain Effect */}
                <div className="absolute inset-0 opacity-30">
                  <div className="h-full w-full bg-gradient-to-r from-transparent via-amber-300 to-transparent"></div>
                  <div className="absolute top-0 h-full w-full bg-gradient-to-b from-transparent via-amber-300 to-transparent opacity-50"></div>
                </div>
              </div>

              {/* 3D Effect Lines */}
              <div className="absolute -top-1 -right-1 w-full h-full border-2 border-amber-400 rounded-lg opacity-60 transform rotate-1"></div>
            </div>

            {/* 3D DOORLY Text */}
            <div className="text-center animate-fade-in-up-delay">
              <h1 className="text-6xl font-black text-white relative">
                {/* Text Shadow for 3D Effect */}
                <span className="absolute top-1 left-1 text-orange-800 -z-10">DOORLY</span>
                <span className="absolute top-2 left-2 text-orange-900 -z-20">DOORLY</span>
                <span className="relative z-10 bg-gradient-to-b from-white to-orange-100 bg-clip-text text-transparent">
                  DOORLY
                </span>
              </h1>
            </div>
          </div>
        </div>

        {/* Welcome Text */}
        <div className="text-center mb-12 animate-fade-in-up-delay-2">
          <h2 className="text-3xl font-bold text-white mb-2">
            Bienvenue au Doorly
          </h2>
          <p className="text-white/80 text-lg">
            Votre solution moderne pour portes et fenêtres
          </p>
        </div>

        {/* Action Buttons */}
        <div className="space-y-4 w-full max-w-sm animate-fade-in-up-delay-3">
          {/* Connect Button */}
          <button
            onClick={onLogin}
            className="w-full py-4 px-8 bg-white text-orange-600 font-bold text-lg rounded-2xl shadow-xl hover:shadow-2xl hover:scale-105 hover:-translate-y-1 transition-all duration-300 relative overflow-hidden group"
          >
            <span className="relative z-10">Se Connecter</span>
            <div className="absolute inset-0 bg-gradient-to-r from-orange-100 to-white opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </button>

          {/* Register Button */}
          <button
            onClick={onRegister}
            className="w-full py-4 px-8 bg-transparent border-3 border-white text-white font-bold text-lg rounded-2xl shadow-xl hover:bg-white hover:text-orange-600 hover:scale-105 hover:-translate-y-1 transition-all duration-300 relative overflow-hidden group"
          >
            <span className="relative z-10">S'inscrire</span>
            <div className="absolute inset-0 bg-white opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </button>
        </div>

        {/* Decorative Elements */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-fade-in-up-delay-4">
          <div className="flex space-x-2">
            <div className="w-2 h-2 bg-white/40 rounded-full animate-pulse"></div>
            <div className="w-2 h-2 bg-white/60 rounded-full animate-pulse" style={{ animationDelay: '0.5s' }}></div>
            <div className="w-2 h-2 bg-white/80 rounded-full animate-pulse" style={{ animationDelay: '1s' }}></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WelcomeScreen;
