import React, { useEffect } from 'react';

interface SplashScreenProps {
  onSwipeRight: () => void;
}

const SplashScreen: React.FC<SplashScreenProps> = ({ onSwipeRight }) => {
  useEffect(() => {
    let startX = 0;
    let startY = 0;

    const handleTouchStart = (e: TouchEvent) => {
      startX = e.touches[0].clientX;
      startY = e.touches[0].clientY;
    };

    const handleTouchEnd = (e: TouchEvent) => {
      const endX = e.changedTouches[0].clientX;
      const endY = e.changedTouches[0].clientY;
      
      const deltaX = endX - startX;
      const deltaY = endY - startY;
      
      // Swipe right detection
      if (deltaX > 50 && Math.abs(deltaY) < 100) {
        onSwipeRight();
      }
    };

    document.addEventListener('touchstart', handleTouchStart);
    document.addEventListener('touchend', handleTouchEnd);

    return () => {
      document.removeEventListener('touchstart', handleTouchStart);
      document.removeEventListener('touchend', handleTouchEnd);
    };
  }, [onSwipeRight]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-400 via-orange-500 to-orange-600 relative overflow-hidden flex items-center justify-center">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-20 left-10 w-32 h-32 bg-white rounded-full blur-xl animate-pulse"></div>
        <div className="absolute top-40 right-20 w-24 h-24 bg-white rounded-full blur-lg animate-pulse delay-1000"></div>
        <div className="absolute bottom-32 left-1/4 w-40 h-40 bg-white rounded-full blur-2xl animate-pulse delay-2000"></div>
        <div className="absolute bottom-20 right-10 w-28 h-28 bg-white rounded-full blur-xl animate-pulse delay-3000"></div>
      </div>

      {/* Diagonal Lines Pattern */}
      <div className="absolute inset-0 opacity-5">
        {Array.from({ length: 15 }).map((_, i) => (
          <div
            key={i}
            className="absolute h-full w-0.5 bg-white transform rotate-45 animate-pulse"
            style={{
              left: `${i * 12}%`,
              animationDelay: `${i * 0.2}s`,
            }}
          />
        ))}
      </div>

      {/* Main Content */}
      <div className="relative z-10 text-center px-6 animate-fade-in">
        {/* Logo Doorly en Bois 3D */}
        <div className="mb-8 animate-float">
          {/* Porte en Bois 3D */}
          <div className="relative w-32 h-40 mx-auto mb-6">
            {/* Ombre de la porte */}
            <div className="absolute top-4 left-4 w-full h-full bg-black/40 rounded-2xl transform rotate-3 blur-2xl"></div>
            
            {/* Porte principale */}
            <div className="relative w-full h-full bg-gradient-to-br from-amber-100 via-amber-200 to-amber-300 rounded-2xl shadow-2xl transform -rotate-2 border-4 border-amber-400">
              {/* Cadre de porte */}
              <div className="absolute inset-3 border-3 border-amber-500 rounded-xl"></div>
              
              {/* Poignée de porte */}
              <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
                <div className="w-3 h-6 bg-gradient-to-br from-yellow-700 to-yellow-900 rounded-full shadow-xl"></div>
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-1.5 h-1.5 bg-yellow-950 rounded-full"></div>
              </div>
              
              {/* Texture bois */}
              <div className="absolute inset-0 opacity-60">
                <div className="h-full w-full bg-gradient-to-r from-transparent via-amber-400 to-transparent"></div>
                <div className="absolute top-0 h-full w-full bg-gradient-to-b from-transparent via-amber-400 to-transparent opacity-70"></div>
                {/* Lignes de bois */}
                <div className="absolute top-1/4 left-2 right-2 h-0.5 bg-amber-600 opacity-40"></div>
                <div className="absolute top-1/2 left-2 right-2 h-0.5 bg-amber-600 opacity-40"></div>
                <div className="absolute top-3/4 left-2 right-2 h-0.5 bg-amber-600 opacity-40"></div>
              </div>
              
              {/* Effet brillant */}
              <div className="absolute inset-0 bg-gradient-to-br from-white/30 to-transparent rounded-2xl"></div>
            </div>
            
            {/* Effet 3D supplémentaire */}
            <div className="absolute -top-2 -right-2 w-full h-full border-3 border-amber-500 rounded-2xl opacity-60 transform rotate-2"></div>
            <div className="absolute -bottom-1 -left-1 w-full h-full border-2 border-amber-600 rounded-2xl opacity-40 transform -rotate-1"></div>
          </div>
          
          {/* Texte DOORLY en 3D */}
          <div className="relative">
            {/* Ombre du texte */}
            <h1 className="absolute top-1 left-1 text-6xl font-black text-amber-900/50 blur-sm">
              DOORLY
            </h1>
            {/* Texte principal */}
            <h1 className="relative text-6xl font-black text-amber-100 drop-shadow-2xl">
              DOORLY
            </h1>
            {/* Effet brillant sur le texte */}
            <div className="absolute inset-0 text-6xl font-black bg-gradient-to-t from-transparent to-white/20 bg-clip-text text-transparent">
              DOORLY
            </div>
          </div>
        </div>

        {/* Message de bienvenue */}
        <div className="text-center">
          <h2 className="text-2xl font-bold text-white mb-2 drop-shadow-lg">
            Bienvenue au Doorly
          </h2>
          <p className="text-white/80 text-lg mb-8">
            Glissez vers la droite pour continuer
          </p>
          
          {/* Indicateur de swipe */}
          <div className="flex items-center justify-center space-x-2 animate-bounce">
            <div className="w-2 h-2 bg-white rounded-full"></div>
            <div className="w-4 h-0.5 bg-white rounded-full"></div>
            <div className="w-0 h-0 border-l-4 border-l-white border-t-2 border-t-transparent border-b-2 border-b-transparent"></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SplashScreen;
