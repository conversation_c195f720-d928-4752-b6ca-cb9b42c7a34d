import React from 'react';
import { ArrowRight } from 'lucide-react';
import <PERSON><PERSON><PERSON><PERSON> from './DoorlyLogo';

interface SplashScreenProps {
  onNext: () => void;
}

const SplashScreen: React.FC<SplashScreenProps> = ({ onNext }) => {

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-400 via-orange-500 to-orange-600 relative overflow-hidden flex items-center justify-center">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-20 left-10 w-32 h-32 bg-white rounded-full blur-xl animate-pulse"></div>
        <div className="absolute top-40 right-20 w-24 h-24 bg-white rounded-full blur-lg animate-pulse delay-1000"></div>
        <div className="absolute bottom-32 left-1/4 w-40 h-40 bg-white rounded-full blur-2xl animate-pulse delay-2000"></div>
        <div className="absolute bottom-20 right-10 w-28 h-28 bg-white rounded-full blur-xl animate-pulse delay-3000"></div>
      </div>

      {/* Diagonal Lines Pattern */}
      <div className="absolute inset-0 opacity-5">
        {Array.from({ length: 15 }).map((_, i) => (
          <div
            key={i}
            className="absolute h-full w-0.5 bg-white transform rotate-45 animate-pulse"
            style={{
              left: `${i * 12}%`,
              animationDelay: `${i * 0.2}s`,
            }}
          />
        ))}
      </div>

      {/* Main Content */}
      <div className="relative z-10 text-center px-6 animate-fade-in">
        {/* Logo Doorly - EXACTEMENT le même partout */}
        <div className="mb-8 animate-float">
          <DoorlyLogo size="xlarge" showText={true} textColor="text-amber-100" />
        </div>

        {/* Message de bienvenue */}
        <div className="text-center">
          <h2 className="text-2xl font-bold text-white mb-2 drop-shadow-lg">
            Bienvenue au Doorly
          </h2>
          <p className="text-white/80 text-lg mb-8">
            Cliquez sur la flèche pour continuer
          </p>

          {/* Bouton flèche */}
          <button
            onClick={onNext}
            className="group bg-white/20 backdrop-blur-sm border-2 border-white/30 rounded-full p-4 hover:bg-white/30 hover:scale-110 transition-all duration-300 shadow-2xl animate-pulse hover:animate-none"
          >
            <ArrowRight className="w-8 h-8 text-white group-hover:text-white/90 transition-colors duration-300" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default SplashScreen;
