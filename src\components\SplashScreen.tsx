import React, { useEffect } from 'react';
import <PERSON><PERSON><PERSON><PERSON> from './DoorlyLogo';

interface SplashScreenProps {
  onSwipeRight: () => void;
}

const SplashScreen: React.FC<SplashScreenProps> = ({ onSwipeRight }) => {
  useEffect(() => {
    let startX = 0;
    let startY = 0;

    const handleTouchStart = (e: TouchEvent) => {
      startX = e.touches[0].clientX;
      startY = e.touches[0].clientY;
    };

    const handleTouchEnd = (e: TouchEvent) => {
      const endX = e.changedTouches[0].clientX;
      const endY = e.changedTouches[0].clientY;
      
      const deltaX = endX - startX;
      const deltaY = endY - startY;
      
      // Swipe right detection
      if (deltaX > 50 && Math.abs(deltaY) < 100) {
        onSwipeRight();
      }
    };

    document.addEventListener('touchstart', handleTouchStart);
    document.addEventListener('touchend', handleTouchEnd);

    return () => {
      document.removeEventListener('touchstart', handleTouchStart);
      document.removeEventListener('touchend', handleTouchEnd);
    };
  }, [onSwipeRight]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-400 via-orange-500 to-orange-600 relative overflow-hidden flex items-center justify-center">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-20 left-10 w-32 h-32 bg-white rounded-full blur-xl animate-pulse"></div>
        <div className="absolute top-40 right-20 w-24 h-24 bg-white rounded-full blur-lg animate-pulse delay-1000"></div>
        <div className="absolute bottom-32 left-1/4 w-40 h-40 bg-white rounded-full blur-2xl animate-pulse delay-2000"></div>
        <div className="absolute bottom-20 right-10 w-28 h-28 bg-white rounded-full blur-xl animate-pulse delay-3000"></div>
      </div>

      {/* Diagonal Lines Pattern */}
      <div className="absolute inset-0 opacity-5">
        {Array.from({ length: 15 }).map((_, i) => (
          <div
            key={i}
            className="absolute h-full w-0.5 bg-white transform rotate-45 animate-pulse"
            style={{
              left: `${i * 12}%`,
              animationDelay: `${i * 0.2}s`,
            }}
          />
        ))}
      </div>

      {/* Main Content */}
      <div className="relative z-10 text-center px-6 animate-fade-in">
        {/* Logo Doorly - EXACTEMENT le même partout */}
        <div className="mb-8 animate-float">
          <DoorlyLogo size="xlarge" showText={true} textColor="text-amber-100" />
        </div>

        {/* Message de bienvenue */}
        <div className="text-center">
          <h2 className="text-2xl font-bold text-white mb-2 drop-shadow-lg">
            Bienvenue au Doorly
          </h2>
          <p className="text-white/80 text-lg mb-8">
            Glissez vers la droite pour continuer
          </p>
          
          {/* Indicateur de swipe */}
          <div className="flex items-center justify-center space-x-2 animate-bounce">
            <div className="w-2 h-2 bg-white rounded-full"></div>
            <div className="w-4 h-0.5 bg-white rounded-full"></div>
            <div className="w-0 h-0 border-l-4 border-l-white border-t-2 border-t-transparent border-b-2 border-b-transparent"></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SplashScreen;
