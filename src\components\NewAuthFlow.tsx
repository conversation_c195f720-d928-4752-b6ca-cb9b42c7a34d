import React, { useState } from 'react';
import SplashScreen from './SplashScreen';
import WelcomeLoginPage from './WelcomeLoginPage';
import RegisterPageNew from './RegisterPageNew';

type AuthView = 'splash' | 'welcome' | 'register';

const NewAuthFlow: React.FC = () => {
  const [currentView, setCurrentView] = useState<AuthView>('splash');

  const handleNext = () => {
    if (currentView === 'splash') {
      setCurrentView('welcome');
    }
  };

  const handleGoToRegister = () => {
    setCurrentView('register');
  };

  const handleGoToLogin = () => {
    setCurrentView('welcome');
  };

  const handleBack = () => {
    setCurrentView('welcome');
  };

  return (
    <div className="min-h-screen">
      {currentView === 'splash' && (
        <SplashScreen onNext={handleNext} />
      )}
      
      {currentView === 'welcome' && (
        <WelcomeLoginPage 
          onBack={handleBack}
          onRegister={handleGoToRegister}
        />
      )}
      
      {currentView === 'register' && (
        <RegisterPageNew 
          onBack={handleBack}
          onLogin={handleGoToLogin}
        />
      )}
    </div>
  );
};

export default NewAuthFlow;
