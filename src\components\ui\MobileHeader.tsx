import React from 'react';
import { clsx } from 'clsx';
import { ArrowLeft, MoreVertical, Search, Bell, ShoppingBag } from 'lucide-react';
import Button from './Button';

interface MobileHeaderProps {
  title?: string;
  subtitle?: string;
  showBackButton?: boolean;
  onBackClick?: () => void;
  showSearch?: boolean;
  onSearchClick?: () => void;
  showNotifications?: boolean;
  notificationCount?: number;
  onNotificationClick?: () => void;
  showCart?: boolean;
  cartCount?: number;
  onCartClick?: () => void;
  rightAction?: React.ReactNode;
  className?: string;
  variant?: 'default' | 'transparent' | 'glass';
}

const MobileHeader: React.FC<MobileHeaderProps> = ({
  title,
  subtitle,
  showBackButton = false,
  onBackClick,
  showSearch = false,
  onSearchClick,
  showNotifications = false,
  notificationCount = 0,
  onNotificationClick,
  showCart = false,
  cartCount = 0,
  onCartClick,
  rightAction,
  className,
  variant = 'default',
}) => {
  const variantClasses = {
    default: 'bg-white border-b border-gray-200',
    transparent: 'bg-transparent',
    glass: 'glass border-b border-white/20',
  };

  return (
    <header
      className={clsx(
        'sticky top-0 z-40 w-full transition-all duration-300',
        'safe-area-pt',
        variantClasses[variant],
        className
      )}
    >
      <div className="flex items-center justify-between px-4 py-3">
        {/* Left Section */}
        <div className="flex items-center space-x-3 flex-1">
          {showBackButton && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onBackClick}
              className="p-2 rounded-full"
            >
              <ArrowLeft className="w-5 h-5" />
            </Button>
          )}
          
          <div className="flex-1 min-w-0">
            {title && (
              <h1 className="text-lg font-semibold text-gray-900 truncate">
                {title}
              </h1>
            )}
            {subtitle && (
              <p className="text-sm text-gray-600 truncate">
                {subtitle}
              </p>
            )}
          </div>
        </div>

        {/* Right Section */}
        <div className="flex items-center space-x-2">
          {showSearch && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onSearchClick}
              className="p-2 rounded-full"
            >
              <Search className="w-5 h-5" />
            </Button>
          )}

          {showNotifications && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onNotificationClick}
              className="relative p-2 rounded-full"
            >
              <Bell className="w-5 h-5" />
              {notificationCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                  {notificationCount > 99 ? '99+' : notificationCount}
                </span>
              )}
            </Button>
          )}

          {showCart && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onCartClick}
              className="relative p-2 rounded-full"
            >
              <ShoppingBag className="w-5 h-5" />
              {cartCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-orange-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                  {cartCount > 99 ? '99+' : cartCount}
                </span>
              )}
            </Button>
          )}

          {rightAction}
        </div>
      </div>
    </header>
  );
};

// Composant de recherche mobile
interface MobileSearchBarProps {
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  onFocus?: () => void;
  onBlur?: () => void;
  className?: string;
}

export const MobileSearchBar: React.FC<MobileSearchBarProps> = ({
  placeholder = "Rechercher...",
  value,
  onChange,
  onFocus,
  onBlur,
  className,
}) => {
  return (
    <div className={clsx('px-4 pb-3', className)}>
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
        <input
          type="text"
          placeholder={placeholder}
          value={value}
          onChange={(e) => onChange?.(e.target.value)}
          onFocus={onFocus}
          onBlur={onBlur}
          className="w-full pl-10 pr-4 py-3 bg-gray-100 rounded-2xl border-none focus:outline-none focus:ring-2 focus:ring-orange-500 transition-all duration-300"
        />
      </div>
    </div>
  );
};

// Composant de tabs mobile
interface MobileTabsProps {
  tabs: Array<{
    id: string;
    label: string;
    count?: number;
  }>;
  activeTab: string;
  onTabChange: (tabId: string) => void;
  className?: string;
}

export const MobileTabs: React.FC<MobileTabsProps> = ({
  tabs,
  activeTab,
  onTabChange,
  className,
}) => {
  return (
    <div className={clsx('px-4 pb-3', className)}>
      <div className="flex space-x-1 bg-gray-100 rounded-2xl p-1">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => onTabChange(tab.id)}
            className={clsx(
              'flex-1 flex items-center justify-center px-3 py-2 rounded-xl',
              'text-sm font-medium transition-all duration-300',
              {
                'bg-white text-orange-600 shadow-sm': activeTab === tab.id,
                'text-gray-600 hover:text-gray-900': activeTab !== tab.id,
              }
            )}
          >
            {tab.label}
            {tab.count !== undefined && tab.count > 0 && (
              <span className={clsx(
                'ml-2 px-2 py-0.5 rounded-full text-xs',
                {
                  'bg-orange-100 text-orange-600': activeTab === tab.id,
                  'bg-gray-200 text-gray-600': activeTab !== tab.id,
                }
              )}>
                {tab.count}
              </span>
            )}
          </button>
        ))}
      </div>
    </div>
  );
};

export default MobileHeader;
