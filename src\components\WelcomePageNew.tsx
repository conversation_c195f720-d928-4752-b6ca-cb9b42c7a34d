import React, { useState, useEffect } from 'react';
import {
  ChevronUp,
  ArrowRight,
  Sparkles,
  Zap,
  Shield,
  Star,
  Play,
  Volume2,
  VolumeX,
  DoorOpen,
  Home,
  Smartphone,
  CreditCard,
  QrCode,
  Users
} from 'lucide-react';

interface WelcomePageProps {
  onEnter: () => void;
}

const WelcomePage: React.FC<WelcomePageProps> = ({ onEnter }) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [showSwipeHint, setShowSwipeHint] = useState(false);
  const [currentScene, setCurrentScene] = useState(0);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  const scenes = [
    {
      title: "DOORLY",
      subtitle: "L'Excellence en Portes & Fenêtres",
      description: "Votre partenaire de confiance pour tous vos projets",
      color: "from-orange-400 via-orange-500 to-orange-600",
      bgColor: "from-orange-900/20 via-orange-800/10 to-black",
      icon: DoorOpen
    },
    {
      title: "MOBILE",
      subtitle: "Application Ultra-Moderne",
      description: "Design révolutionnaire pour une expérience unique",
      color: "from-orange-300 via-orange-400 to-orange-500",
      bgColor: "from-orange-800/20 via-orange-700/10 to-black",
      icon: Smartphone
    },
    {
      title: "PREMIUM",
      subtitle: "Fonctionnalités Avancées",
      description: "Paiement intégré, QR codes et bien plus",
      color: "from-orange-500 via-orange-600 to-orange-700",
      bgColor: "from-orange-900/30 via-orange-800/15 to-black",
      icon: Star
    }
  ];

  useEffect(() => {
    setTimeout(() => setIsLoaded(true), 300);
    setTimeout(() => setShowSwipeHint(true), 2500);

    const sceneInterval = setInterval(() => {
      setCurrentScene((prev) => (prev + 1) % scenes.length);
    }, 4000);

    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    window.addEventListener('mousemove', handleMouseMove);

    return () => {
      clearInterval(sceneInterval);
      window.removeEventListener('mousemove', handleMouseMove);
    };
  }, [scenes.length]);

  const handleSwipeUp = (e: React.TouchEvent) => {
    const touch = e.touches[0];
    const startY = touch.clientY;

    const handleTouchMove = (moveEvent: TouchEvent) => {
      const currentY = moveEvent.touches[0].clientY;
      const deltaY = startY - currentY;

      if (deltaY > 80) {
        onEnter();
        document.removeEventListener('touchmove', handleTouchMove);
      }
    };

    document.addEventListener('touchmove', handleTouchMove);
    document.addEventListener('touchend', () => {
      document.removeEventListener('touchmove', handleTouchMove);
    }, { once: true });
  };

  return (
    <div
      className={`min-h-screen bg-gradient-to-br ${scenes[currentScene].bgColor} flex items-center justify-center relative overflow-hidden transition-all duration-1000`}
      onTouchStart={handleSwipeUp}
      onClick={onEnter}
      style={{
        background: `radial-gradient(circle at ${mousePosition.x}px ${mousePosition.y}px, rgba(255,165,0,0.1) 0%, transparent 50%), linear-gradient(135deg, #1a1a1a 0%, #2d1810 50%, #000000 100%)`
      }}
    >
      {/* Arrière-plan animé ultra-moderne */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Particules flottantes */}
        {[...Array(50)].map((_, i) => (
          <div
            key={i}
            className="absolute rounded-full opacity-20 animate-float"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              width: `${Math.random() * 8 + 2}px`,
              height: `${Math.random() * 8 + 2}px`,
              background: `linear-gradient(45deg, #ff6b35, #f7931e, #ffb347)`,
              animationDelay: `${Math.random() * 5}s`,
              animationDuration: `${Math.random() * 3 + 2}s`
            }}
          />
        ))}

        {/* Cercles géométriques */}
        <div className="absolute top-1/4 left-1/4 w-96 h-96 border border-orange-500/10 rounded-full animate-spin-slow"></div>
        <div className="absolute bottom-1/4 right-1/4 w-64 h-64 border border-orange-400/15 rounded-full animate-spin-reverse"></div>

        {/* Lignes géométriques */}
        <div className="absolute inset-0">
          {[...Array(6)].map((_, i) => (
            <div
              key={i}
              className="absolute h-px bg-gradient-to-r from-transparent via-orange-500/20 to-transparent animate-pulse"
              style={{
                top: `${20 + i * 15}%`,
                left: '0',
                right: '0',
                animationDelay: `${i * 0.5}s`
              }}
            />
          ))}
        </div>
      </div>

      {/* Contenu principal */}
      <div className="text-center z-10 px-6 relative max-w-md mx-auto">
        {/* Logo principal ultra-moderne */}
        <div className={`relative mb-12 transform transition-all duration-1000 ${isLoaded ? 'scale-100 opacity-100 translate-y-0' : 'scale-75 opacity-0 translate-y-12'}`}>

          {/* Logo DOORLY 3D */}
          <div className="relative mx-auto w-40 h-40 mb-8">
            {/* Cercle de fond avec effet glassmorphism */}
            <div className="absolute inset-0 bg-gradient-to-br from-orange-500/20 via-orange-400/10 to-transparent rounded-full backdrop-blur-xl border border-orange-300/30 shadow-2xl"></div>

            {/* Logo porte 3D */}
            <div className="relative w-24 h-32 mx-auto mt-4">
              <div className={`w-full h-full bg-gradient-to-br ${scenes[currentScene].color} rounded-r-3xl shadow-2xl transform perspective-1000 rotate-y-12 relative overflow-hidden`}>
                {/* Effet de profondeur */}
                <div className="absolute inset-0 bg-gradient-to-b from-white/40 via-transparent to-black/20 rounded-r-3xl" />

                {/* Poignée de porte */}
                <div className="absolute top-1/2 right-3 w-3 h-3 bg-gradient-to-br from-yellow-300 to-yellow-500 rounded-full transform -translate-y-1/2 shadow-lg" />

                {/* Reflets */}
                <div className="absolute top-4 left-2 w-1 h-8 bg-white/60 rounded-full blur-sm" />
                <div className="absolute bottom-4 right-1 w-2 h-4 bg-white/30 rounded-full blur-sm" />
              </div>

              {/* Ombre portée */}
              <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-20 h-4 bg-black/20 rounded-full blur-lg"></div>
            </div>

            {/* Icône de scène flottante */}
            <div className="absolute -top-2 -right-2 w-12 h-12 bg-gradient-to-br from-orange-400 to-orange-600 rounded-full flex items-center justify-center shadow-xl animate-bounce-slow">
              <scenes[currentScene].icon className="w-6 h-6 text-white" />
            </div>
          </div>

          {/* Titre avec effet néon */}
          <h1 className={`text-5xl sm:text-6xl font-black bg-gradient-to-r ${scenes[currentScene].color} bg-clip-text text-transparent tracking-wider mb-4 relative`}>
            {scenes[currentScene].title}
            <div className="absolute inset-0 bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent blur-sm opacity-50 -z-10">
              {scenes[currentScene].title}
            </div>
          </h1>

          {/* Sous-titre */}
          <p className="text-xl sm:text-2xl font-bold text-white/95 mb-3 tracking-wide">
            {scenes[currentScene].subtitle}
          </p>

          {/* Description */}
          <p className="text-sm text-white/70 mb-8 leading-relaxed">
            {scenes[currentScene].description}
          </p>

          {/* Indicateurs de fonctionnalités modernes */}
          <div className="grid grid-cols-3 gap-4 mb-10">
            {[
              { icon: CreditCard, label: 'Paiement', color: 'from-green-400 to-green-600' },
              { icon: QrCode, label: 'QR Code', color: 'from-blue-400 to-blue-600' },
              { icon: Users, label: 'Multi-user', color: 'from-purple-400 to-purple-600' }
            ].map((feature, i) => (
              <div key={i} className="group relative">
                <div className="bg-black/40 backdrop-blur-2xl border border-white/20 rounded-2xl p-4 text-center transition-all duration-300 group-hover:scale-105 group-hover:bg-black/50">
                  <div className={`w-8 h-8 bg-gradient-to-r ${feature.color} rounded-xl flex items-center justify-center mx-auto mb-3 shadow-lg`}>
                    <feature.icon className="w-4 h-4 text-white" />
                  </div>
                  <span className="text-white/90 text-xs font-bold">{feature.label}</span>
                </div>
                {/* Effet de lueur au survol */}
                <div className={`absolute inset-0 bg-gradient-to-r ${feature.color} rounded-2xl opacity-0 group-hover:opacity-20 transition-opacity duration-300 blur-xl -z-10`}></div>
              </div>
            ))}
          </div>
        </div>

        {/* Navigation ultra-moderne */}
        <div className={`fixed bottom-8 left-1/2 transform -translate-x-1/2 transition-all duration-1000 ${showSwipeHint ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-12'}`}>
          <div className="bg-black/30 backdrop-blur-3xl border border-orange-300/30 rounded-3xl px-8 py-5 flex items-center space-x-6 shadow-2xl">

            {/* Indicateur de swipe */}
            <div className="flex flex-col items-center space-y-3">
              <div className="relative">
                <ChevronUp className="w-7 h-7 text-orange-400 animate-bounce" />
                <div className="absolute inset-0 bg-orange-400 blur-lg opacity-50 animate-pulse"></div>
              </div>
              <div className="flex space-x-1">
                {[...Array(3)].map((_, i) => (
                  <div
                    key={i}
                    className="w-2 h-2 bg-gradient-to-r from-orange-400 to-orange-600 rounded-full animate-pulse"
                    style={{ animationDelay: `${i * 0.3}s` }}
                  />
                ))}
              </div>
            </div>

            {/* Texte d'instruction */}
            <div className="text-center">
              <p className="text-white font-bold text-base tracking-wide">COMMENCER</p>
              <p className="text-orange-300 text-sm font-medium">Glissez ↑ ou touchez</p>
            </div>

            {/* Bouton d'action principal */}
            <button
              onClick={onEnter}
              className="relative w-14 h-14 bg-gradient-to-r from-orange-500 via-orange-600 to-orange-700 rounded-2xl flex items-center justify-center transition-all duration-300 hover:scale-110 hover:rotate-3 shadow-2xl shadow-orange-500/50 group"
            >
              <Play className="w-6 h-6 text-white group-hover:scale-110 transition-transform duration-300" />

              {/* Effet de lueur */}
              <div className="absolute inset-0 bg-gradient-to-r from-orange-400 to-orange-600 rounded-2xl opacity-0 group-hover:opacity-50 blur-xl transition-opacity duration-300"></div>

              {/* Cercles d'animation */}
              <div className="absolute inset-0 rounded-2xl border-2 border-orange-400 animate-ping opacity-20"></div>
            </button>
          </div>

          {/* Indicateurs de scène */}
          <div className="flex justify-center mt-4 space-x-2">
            {scenes.map((_, i) => (
              <div
                key={i}
                className={`w-2 h-2 rounded-full transition-all duration-300 ${
                  i === currentScene
                    ? 'bg-orange-400 w-8'
                    : 'bg-white/30 hover:bg-white/50'
                }`}
              />
            ))}
          </div>
        </div>
      </div>

      {/* Styles CSS personnalisés */}
      <style jsx>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-20px) rotate(180deg); }
        }

        @keyframes spin-slow {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }

        @keyframes spin-reverse {
          from { transform: rotate(360deg); }
          to { transform: rotate(0deg); }
        }

        @keyframes bounce-slow {
          0%, 100% { transform: translateY(0); }
          50% { transform: translateY(-10px); }
        }

        .animate-float {
          animation: float 3s ease-in-out infinite;
        }

        .animate-spin-slow {
          animation: spin-slow 20s linear infinite;
        }

        .animate-spin-reverse {
          animation: spin-reverse 15s linear infinite;
        }

        .animate-bounce-slow {
          animation: bounce-slow 2s ease-in-out infinite;
        }

        .perspective-1000 {
          perspective: 1000px;
        }

        .rotate-y-12 {
          transform: rotateY(12deg);
        }
      `}</style>
    </div>
  );
};

export default WelcomePage;
