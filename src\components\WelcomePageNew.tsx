import React, { useState, useEffect } from 'react';
import { 
  ChevronUp, 
  ArrowRight, 
  Sparkles, 
  Zap, 
  Shield, 
  Star,
  Play,
  Volume2,
  VolumeX
} from 'lucide-react';

interface WelcomePageProps {
  onEnter: () => void;
}

const WelcomePage: React.FC<WelcomePageProps> = ({ onEnter }) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [showSwipeHint, setShowSwipeHint] = useState(false);
  const [currentScene, setCurrentScene] = useState(0);

  const scenes = [
    {
      title: "DOORLY",
      subtitle: "L'Excellence Redéfinie",
      color: "from-orange-400 via-orange-500 to-orange-600",
      bgColor: "from-gray-900 via-gray-800 to-black"
    },
    {
      title: "INNOVATION",
      subtitle: "Technologie Avancée",
      color: "from-cyan-400 via-blue-500 to-purple-600",
      bgColor: "from-blue-900 via-purple-900 to-black"
    },
    {
      title: "PREMIUM",
      subtitle: "Qualité Supérieure",
      color: "from-purple-400 via-pink-500 to-red-600",
      bgColor: "from-purple-900 via-pink-900 to-black"
    }
  ];

  useEffect(() => {
    setTimeout(() => setIsLoaded(true), 500);
    setTimeout(() => setShowSwipeHint(true), 3000);
    
    const sceneInterval = setInterval(() => {
      setCurrentScene((prev) => (prev + 1) % scenes.length);
    }, 5000);

    return () => clearInterval(sceneInterval);
  }, [scenes.length]);

  const handleSwipeUp = (e: React.TouchEvent) => {
    const touch = e.touches[0];
    const startY = touch.clientY;

    const handleTouchMove = (moveEvent: TouchEvent) => {
      const currentY = moveEvent.touches[0].clientY;
      const deltaY = startY - currentY;

      if (deltaY > 100) {
        onEnter();
        document.removeEventListener('touchmove', handleTouchMove);
      }
    };

    document.addEventListener('touchmove', handleTouchMove);
    document.addEventListener('touchend', () => {
      document.removeEventListener('touchmove', handleTouchMove);
    }, { once: true });
  };

  return (
    <div 
      className={`min-h-screen bg-gradient-to-br ${scenes[currentScene].bgColor} flex items-center justify-center relative overflow-hidden transition-all duration-1000`}
      onTouchStart={handleSwipeUp}
      onClick={onEnter}
    >
      {/* Particules d'arrière-plan */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(30)].map((_, i) => (
          <div
            key={i}
            className="absolute w-2 h-2 bg-orange-400 rounded-full opacity-30 animate-pulse"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`
            }}
          />
        ))}
      </div>

      {/* Contenu principal */}
      <div className="text-center z-10 px-4 relative">
        {/* Logo principal */}
        <div className={`relative mb-8 transform transition-all duration-1000 ${isLoaded ? 'scale-100 opacity-100 translate-y-0' : 'scale-75 opacity-0 translate-y-8'}`}>
          {/* Logo DOORLY */}
          <div className="relative mx-auto w-32 h-32 mb-6">
            <div className={`w-20 h-28 bg-gradient-to-br ${scenes[currentScene].color} rounded-r-2xl shadow-2xl mx-auto`}>
              <div className="absolute inset-0 bg-gradient-to-b from-white/30 via-transparent to-white/10 rounded-r-2xl" />
              <div className="absolute top-1/2 right-2 w-2 h-2 bg-yellow-400 rounded-full transform -translate-y-1/2" />
            </div>
          </div>

          {/* Titre */}
          <h1 className={`text-4xl sm:text-5xl font-black bg-gradient-to-r ${scenes[currentScene].color} bg-clip-text text-transparent tracking-wider mb-4`}>
            {scenes[currentScene].title}
          </h1>

          {/* Sous-titre */}
          <p className="text-lg sm:text-xl font-bold text-white/90 mb-6">
            {scenes[currentScene].subtitle}
          </p>

          {/* Indicateurs de fonctionnalités */}
          <div className="grid grid-cols-3 gap-3 mb-8">
            {[
              { icon: Shield, label: 'Sécurisé', color: 'text-green-400' },
              { icon: Zap, label: 'Rapide', color: 'text-yellow-400' },
              { icon: Sparkles, label: 'Premium', color: 'text-purple-400' }
            ].map((feature, i) => (
              <div key={i} className="bg-black/30 backdrop-blur-xl border border-white/20 rounded-xl p-3 text-center">
                <feature.icon className={`w-6 h-6 ${feature.color} mx-auto mb-2`} />
                <span className="text-white/80 text-xs font-semibold">{feature.label}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Navigation simple */}
        <div className={`fixed bottom-6 left-1/2 transform -translate-x-1/2 transition-all duration-1000 ${showSwipeHint ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
          <div className="bg-black/40 backdrop-blur-2xl border border-white/30 rounded-2xl px-6 py-4 flex items-center space-x-4">
            <div className="flex flex-col items-center space-y-2">
              <ChevronUp className="w-6 h-6 text-white animate-bounce" />
              <div className="flex space-x-1">
                {[...Array(3)].map((_, i) => (
                  <div
                    key={i}
                    className="w-1 h-1 bg-orange-400 rounded-full animate-pulse"
                    style={{ animationDelay: `${i * 0.2}s` }}
                  />
                ))}
              </div>
            </div>
            
            <div className="text-center">
              <p className="text-white/95 text-sm font-bold">ENTRER</p>
              <p className="text-white/70 text-xs">Glissez ↑ ou touchez</p>
            </div>
            
            <button
              onClick={onEnter}
              className="w-12 h-12 bg-gradient-to-r from-orange-500 to-orange-600 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110"
            >
              <Play className="w-5 h-5 text-white" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WelcomePage;
