import React from 'react';
import { 
  Home, 
  Search, 
  ShoppingBag, 
  User, 
  Heart,
  <PERSON>ting<PERSON>,
  <PERSON>,
  Star,
  TrendingUp,
  Award
} from 'lucide-react';

interface MobileNavigationProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  cartCount: number;
  favoriteCount: number;
  notificationCount: number;
}

const MobileNavigation: React.FC<MobileNavigationProps> = ({
  activeTab,
  onTabChange,
  cartCount,
  favoriteCount,
  notificationCount
}) => {
  const navItems = [
    {
      id: 'home',
      icon: Home,
      label: 'Accueil',
      color: 'text-orange-500',
      activeColor: 'bg-orange-500'
    },
    {
      id: 'search',
      icon: Search,
      label: 'Recherche',
      color: 'text-blue-500',
      activeColor: 'bg-blue-500'
    },
    {
      id: 'favorites',
      icon: Heart,
      label: 'Favoris',
      color: 'text-pink-500',
      activeColor: 'bg-pink-500',
      badge: favoriteCount
    },
    {
      id: 'cart',
      icon: ShoppingBag,
      label: 'Panier',
      color: 'text-green-500',
      activeColor: 'bg-green-500',
      badge: cartCount
    },
    {
      id: 'profile',
      icon: User,
      label: 'Profil',
      color: 'text-purple-500',
      activeColor: 'bg-purple-500'
    }
  ];

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 safe-area-bottom">
      {/* Navigation principale */}
      <div className="bg-white/95 backdrop-blur-2xl border-t border-orange-200 shadow-2xl">
        <div className="flex items-center justify-around py-2">
          {navItems.map((item) => (
            <button
              key={item.id}
              onClick={() => onTabChange(item.id)}
              className={`relative flex flex-col items-center space-y-1 p-2 rounded-xl transition-all duration-300 touch-optimized ${
                activeTab === item.id
                  ? `${item.activeColor} text-white shadow-lg scale-105`
                  : `${item.color} hover:bg-gray-100`
              }`}
            >
              <div className="relative">
                <item.icon className="w-5 h-5" />
                {item.badge && item.badge > 0 && (
                  <span className="absolute -top-2 -right-2 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center text-xs font-bold text-white">
                    {item.badge > 99 ? '99+' : item.badge}
                  </span>
                )}
              </div>
              <span className="text-xs font-medium">{item.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Barre d'actions rapides */}
      <div className="bg-gray-50/95 backdrop-blur-xl border-t border-gray-200">
        <div className="flex items-center justify-center py-2 space-x-6">
          {/* Notifications */}
          <button
            onClick={() => onTabChange('notifications')}
            className="relative p-2 rounded-full bg-white shadow-md border border-gray-200 text-gray-600 hover:bg-gray-50 transition-all duration-300 touch-optimized"
          >
            <Bell className="w-4 h-4" />
            {notificationCount > 0 && (
              <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full flex items-center justify-center text-xs font-bold text-white">
                {notificationCount > 9 ? '9+' : notificationCount}
              </span>
            )}
          </button>

          {/* Évaluations */}
          <button
            onClick={() => onTabChange('reviews')}
            className="p-2 rounded-full bg-white shadow-md border border-gray-200 text-yellow-500 hover:bg-gray-50 transition-all duration-300 touch-optimized"
          >
            <Star className="w-4 h-4" />
          </button>

          {/* Tendances */}
          <button
            onClick={() => onTabChange('trending')}
            className="p-2 rounded-full bg-white shadow-md border border-gray-200 text-green-500 hover:bg-gray-50 transition-all duration-300 touch-optimized"
          >
            <TrendingUp className="w-4 h-4" />
          </button>

          {/* Récompenses */}
          <button
            onClick={() => onTabChange('rewards')}
            className="p-2 rounded-full bg-white shadow-md border border-gray-200 text-purple-500 hover:bg-gray-50 transition-all duration-300 touch-optimized"
          >
            <Award className="w-4 h-4" />
          </button>

          {/* Paramètres */}
          <button
            onClick={() => onTabChange('settings')}
            className="p-2 rounded-full bg-white shadow-md border border-gray-200 text-gray-600 hover:bg-gray-50 transition-all duration-300 touch-optimized"
          >
            <Settings className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default MobileNavigation;
