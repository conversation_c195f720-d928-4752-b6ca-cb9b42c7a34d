import React, { useState } from 'react';
import { Product, Order } from '../types';
import { 
  X, 
  CreditCard, 
  Banknote, 
  Smartphone, 
  CheckCircle, 
  User, 
  Phone, 
  MapPin,
  Calendar,
  Package,
  Shield,
  Download,
  Share,
  Printer
} from 'lucide-react';

interface ModernOrderModalProps {
  product: Product;
  type: 'buy' | 'reserve';
  onClose: () => void;
  onConfirm: (order: Omit<Order, 'id' | 'date'>) => void;
}

const ModernOrderModal: React.FC<ModernOrderModalProps> = ({ 
  product, 
  type, 
  onClose, 
  onConfirm 
}) => {
  const [step, setStep] = useState(1); // 1: Info, 2: Payment, 3: Receipt
  const [clientName, setClientName] = useState('');
  const [clientPhone, setClientPhone] = useState('');
  const [clientAddress, setClientAddress] = useState('');
  const [quantity, setQuantity] = useState(1);
  const [paymentMethod, setPaymentMethod] = useState('card');
  const [cardNumber, setCardNumber] = useState('');
  const [expiryDate, setExpiryDate] = useState('');
  const [cvv, setCvv] = useState('');
  const [orderData, setOrderData] = useState<Omit<Order, 'id' | 'date'> | null>(null);

  const totalPrice = product.price * quantity;
  const tax = totalPrice * 0.2; // 20% TVA
  const finalPrice = totalPrice + tax;

  const handleNextStep = () => {
    if (step === 1) {
      setStep(2);
    } else if (step === 2) {
      // Simuler le paiement
      const order: Omit<Order, 'id' | 'date'> = {
        clientName,
        clientPhone,
        product,
        quantity,
        totalPrice: finalPrice,
        status: type === 'buy' ? 'purchased' : 'reserved',
        paymentMethod: paymentMethod === 'card' ? 'Carte bancaire' : paymentMethod === 'cash' ? 'Espèces' : 'Mobile Money'
      };
      setOrderData(order);
      onConfirm(order);
      setStep(3);
    }
  };

  const formatCardNumber = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    const matches = v.match(/\d{4,16}/g);
    const match = matches && matches[0] || '';
    const parts = [];
    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4));
    }
    if (parts.length) {
      return parts.join(' ');
    } else {
      return v;
    }
  };

  if (step === 3 && orderData) {
    return (
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
        <div className="bg-white rounded-3xl p-8 max-w-md w-full shadow-2xl border border-orange-200 animate-fade-in-scale">
          <div className="text-center mb-6">
            <div className="w-20 h-20 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse-slow">
              <CheckCircle className="w-10 h-10 text-white" />
            </div>
            <h2 className="text-3xl font-bold text-gray-900 mb-2">
              {type === 'buy' ? '🎉 Achat confirmé!' : '📅 Réservation confirmée!'}
            </h2>
            <p className="text-gray-600">Votre commande a été traitée avec succès</p>
          </div>

          {/* Reçu détaillé */}
          <div className="bg-gradient-to-br from-orange-50 to-orange-100 rounded-2xl p-6 mb-6 border border-orange-200">
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-bold text-gray-900 text-lg">Reçu de commande</h3>
              <span className="text-sm text-gray-600">#{Math.random().toString(36).substr(2, 9).toUpperCase()}</span>
            </div>
            
            <div className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Client:</span>
                <span className="font-medium text-gray-900">{orderData.clientName}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Téléphone:</span>
                <span className="font-medium text-gray-900">{orderData.clientPhone}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Produit:</span>
                <span className="font-medium text-gray-900">{orderData.product.name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Quantité:</span>
                <span className="font-medium text-gray-900">{orderData.quantity}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Prix unitaire:</span>
                <span className="font-medium text-gray-900">{product.price.toLocaleString()} DH</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Sous-total:</span>
                <span className="font-medium text-gray-900">{totalPrice.toLocaleString()} DH</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">TVA (20%):</span>
                <span className="font-medium text-gray-900">{tax.toLocaleString()} DH</span>
              </div>
              <div className="border-t border-orange-300 pt-2">
                <div className="flex justify-between">
                  <span className="font-bold text-gray-900">Total:</span>
                  <span className="font-bold text-orange-600 text-lg">{finalPrice.toLocaleString()} DH</span>
                </div>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Paiement:</span>
                <span className="font-medium text-gray-900">{orderData.paymentMethod}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Statut:</span>
                <span className={`font-medium px-2 py-1 rounded-full text-xs ${
                  orderData.status === 'purchased' 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-orange-100 text-orange-800'
                }`}>
                  {orderData.status === 'purchased' ? 'Payé' : 'Réservé'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Date:</span>
                <span className="font-medium text-gray-900">{new Date().toLocaleDateString('fr-FR')}</span>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex space-x-3 mb-6">
            <button className="flex-1 bg-orange-500 hover:bg-orange-600 text-white font-bold py-3 px-4 rounded-2xl transition-all duration-300 transform hover:scale-105 flex items-center justify-center space-x-2">
              <Download className="w-4 h-4" />
              <span>Télécharger</span>
            </button>
            <button className="flex-1 bg-gray-500 hover:bg-gray-600 text-white font-bold py-3 px-4 rounded-2xl transition-all duration-300 transform hover:scale-105 flex items-center justify-center space-x-2">
              <Share className="w-4 h-4" />
              <span>Partager</span>
            </button>
            <button className="flex-1 bg-blue-500 hover:bg-blue-600 text-white font-bold py-3 px-4 rounded-2xl transition-all duration-300 transform hover:scale-105 flex items-center justify-center space-x-2">
              <Printer className="w-4 h-4" />
              <span>Imprimer</span>
            </button>
          </div>

          {/* Informations importantes */}
          <div className="bg-blue-50 rounded-2xl p-4 mb-6 border border-blue-200">
            <div className="flex items-start space-x-3">
              <Shield className="w-5 h-5 text-blue-500 mt-0.5" />
              <div>
                <h4 className="font-bold text-blue-900 mb-1">Informations importantes</h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• Conservez ce reçu pour toute réclamation</li>
                  <li>• Livraison sous 3-5 jours ouvrables</li>
                  <li>• Garantie constructeur incluse</li>
                  <li>• Service client: +212 5XX XXX XXX</li>
                </ul>
              </div>
            </div>
          </div>

          <button
            onClick={onClose}
            className="w-full bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-bold py-4 px-6 rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-lg"
          >
            Fermer
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-3xl p-8 max-w-md w-full shadow-2xl max-h-[90vh] overflow-y-auto border border-orange-200 animate-fade-in-scale">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-gray-900">
            {type === 'buy' ? '🛒 Acheter' : '📅 Réserver'} - {product.name}
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors duration-200"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Progress bar */}
        <div className="flex items-center justify-center mb-8">
          <div className="flex items-center space-x-4">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
              step >= 1 ? 'bg-orange-500 text-white' : 'bg-gray-200 text-gray-500'
            }`}>1</div>
            <div className={`w-16 h-1 ${step >= 2 ? 'bg-orange-500' : 'bg-gray-200'}`}></div>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
              step >= 2 ? 'bg-orange-500 text-white' : 'bg-gray-200 text-gray-500'
            }`}>2</div>
            <div className={`w-16 h-1 ${step >= 3 ? 'bg-orange-500' : 'bg-gray-200'}`}></div>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
              step >= 3 ? 'bg-green-500 text-white' : 'bg-gray-200 text-gray-500'
            }`}>3</div>
          </div>
        </div>

        {step === 1 && (
          <form onSubmit={(e) => { e.preventDefault(); handleNextStep(); }} className="space-y-6">
            <div className="bg-orange-50 rounded-2xl p-4 border border-orange-200">
              <h3 className="font-bold text-gray-900 mb-2">Détails du produit</h3>
              <div className="flex items-center space-x-4">
                <img src={product.image} alt={product.name} className="w-16 h-16 object-cover rounded-xl" />
                <div>
                  <p className="font-medium text-gray-900">{product.name}</p>
                  <p className="text-sm text-gray-600">{product.size} • {product.color}</p>
                  <p className="text-lg font-bold text-orange-600">{product.price.toLocaleString()} DH</p>
                </div>
              </div>
            </div>

            <div>
              <label className="block text-sm font-bold text-gray-800 mb-2">
                <User className="w-4 h-4 inline mr-2" />
                Nom complet *
              </label>
              <input
                type="text"
                value={clientName}
                onChange={(e) => setClientName(e.target.value)}
                className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-orange-500 transition-all duration-300"
                placeholder="Votre nom complet"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-bold text-gray-800 mb-2">
                <Phone className="w-4 h-4 inline mr-2" />
                Numéro de téléphone *
              </label>
              <input
                type="tel"
                value={clientPhone}
                onChange={(e) => setClientPhone(e.target.value)}
                className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-orange-500 transition-all duration-300"
                placeholder="+212 6XX XXX XXX"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-bold text-gray-800 mb-2">
                <MapPin className="w-4 h-4 inline mr-2" />
                Adresse de livraison
              </label>
              <textarea
                value={clientAddress}
                onChange={(e) => setClientAddress(e.target.value)}
                className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-orange-500 transition-all duration-300"
                placeholder="Votre adresse complète"
                rows={3}
              />
            </div>

            <div>
              <label className="block text-sm font-bold text-gray-800 mb-2">
                <Package className="w-4 h-4 inline mr-2" />
                Quantité
              </label>
              <div className="flex items-center space-x-4">
                <button
                  type="button"
                  onClick={() => setQuantity(Math.max(1, quantity - 1))}
                  className="w-10 h-10 bg-gray-200 hover:bg-gray-300 rounded-xl flex items-center justify-center transition-colors duration-200"
                >
                  -
                </button>
                <span className="text-xl font-bold text-gray-900 w-12 text-center">{quantity}</span>
                <button
                  type="button"
                  onClick={() => setQuantity(Math.min(product.stock, quantity + 1))}
                  className="w-10 h-10 bg-gray-200 hover:bg-gray-300 rounded-xl flex items-center justify-center transition-colors duration-200"
                >
                  +
                </button>
              </div>
            </div>

            <div className="bg-gray-50 rounded-2xl p-4 border border-gray-200">
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Sous-total:</span>
                  <span className="font-medium">{totalPrice.toLocaleString()} DH</span>
                </div>
                <div className="flex justify-between">
                  <span>TVA (20%):</span>
                  <span className="font-medium">{tax.toLocaleString()} DH</span>
                </div>
                <div className="border-t border-gray-300 pt-2">
                  <div className="flex justify-between">
                    <span className="font-bold">Total:</span>
                    <span className="font-bold text-orange-600 text-lg">{finalPrice.toLocaleString()} DH</span>
                  </div>
                </div>
              </div>
            </div>

            <button
              type="submit"
              className="w-full bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-bold py-4 px-6 rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-lg"
            >
              Continuer vers le paiement
            </button>
          </form>
        )}

        {step === 2 && (
          <div className="space-y-6">
            <div className="bg-orange-50 rounded-2xl p-4 border border-orange-200">
              <h3 className="font-bold text-gray-900 mb-2">Récapitulatif</h3>
              <p className="text-sm text-gray-600">{clientName} • {clientPhone}</p>
              <p className="text-sm text-gray-600">{product.name} × {quantity}</p>
              <p className="text-lg font-bold text-orange-600">{finalPrice.toLocaleString()} DH</p>
            </div>

            <div>
              <h3 className="font-bold text-gray-900 mb-4">Méthode de paiement</h3>
              <div className="space-y-3">
                <label className="flex items-center space-x-3 p-4 border border-gray-200 rounded-2xl cursor-pointer hover:bg-gray-50 transition-colors duration-200">
                  <input
                    type="radio"
                    name="payment"
                    value="card"
                    checked={paymentMethod === 'card'}
                    onChange={(e) => setPaymentMethod(e.target.value)}
                    className="text-orange-500"
                  />
                  <CreditCard className="w-5 h-5 text-gray-600" />
                  <span className="font-medium">Carte bancaire</span>
                </label>
                
                <label className="flex items-center space-x-3 p-4 border border-gray-200 rounded-2xl cursor-pointer hover:bg-gray-50 transition-colors duration-200">
                  <input
                    type="radio"
                    name="payment"
                    value="cash"
                    checked={paymentMethod === 'cash'}
                    onChange={(e) => setPaymentMethod(e.target.value)}
                    className="text-orange-500"
                  />
                  <Banknote className="w-5 h-5 text-gray-600" />
                  <span className="font-medium">Espèces à la livraison</span>
                </label>
                
                <label className="flex items-center space-x-3 p-4 border border-gray-200 rounded-2xl cursor-pointer hover:bg-gray-50 transition-colors duration-200">
                  <input
                    type="radio"
                    name="payment"
                    value="mobile"
                    checked={paymentMethod === 'mobile'}
                    onChange={(e) => setPaymentMethod(e.target.value)}
                    className="text-orange-500"
                  />
                  <Smartphone className="w-5 h-5 text-gray-600" />
                  <span className="font-medium">Mobile Money</span>
                </label>
              </div>
            </div>

            {paymentMethod === 'card' && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-bold text-gray-800 mb-2">Numéro de carte</label>
                  <input
                    type="text"
                    value={cardNumber}
                    onChange={(e) => setCardNumber(formatCardNumber(e.target.value))}
                    className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-orange-500 transition-all duration-300"
                    placeholder="1234 5678 9012 3456"
                    maxLength={19}
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-bold text-gray-800 mb-2">Date d'expiration</label>
                    <input
                      type="text"
                      value={expiryDate}
                      onChange={(e) => setExpiryDate(e.target.value)}
                      className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-orange-500 transition-all duration-300"
                      placeholder="MM/AA"
                      maxLength={5}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-bold text-gray-800 mb-2">CVV</label>
                    <input
                      type="text"
                      value={cvv}
                      onChange={(e) => setCvv(e.target.value)}
                      className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-orange-500 transition-all duration-300"
                      placeholder="123"
                      maxLength={3}
                    />
                  </div>
                </div>
              </div>
            )}

            <div className="flex space-x-3">
              <button
                onClick={() => setStep(1)}
                className="flex-1 bg-gray-500 hover:bg-gray-600 text-white font-bold py-4 px-6 rounded-2xl transition-all duration-300"
              >
                Retour
              </button>
              <button
                onClick={handleNextStep}
                className="flex-1 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-bold py-4 px-6 rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-lg"
              >
                {type === 'buy' ? 'Payer maintenant' : 'Confirmer la réservation'}
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ModernOrderModal;
