import React from 'react';

interface DoorlyLogoProps {
  size?: 'small' | 'medium' | 'large' | 'xlarge';
  showText?: boolean;
  textColor?: string;
}

const DoorlyLogo: React.FC<DoorlyLogoProps> = ({ 
  size = 'medium', 
  showText = true, 
  textColor = 'text-amber-100' 
}) => {
  // Tailles pour la porte
  const doorSizes = {
    small: { width: 'w-8', height: 'h-10', textSize: 'text-lg' },
    medium: { width: 'w-12', height: 'h-16', textSize: 'text-2xl' },
    large: { width: 'w-20', height: 'h-24', textSize: 'text-4xl' },
    xlarge: { width: 'w-32', height: 'h-40', textSize: 'text-6xl' }
  };

  const { width, height, textSize } = doorSizes[size];

  return (
    <div className="flex flex-col items-center">
      {/* Logo Doorly en Bois - EXACTEMENT comme l'image fournie */}
      <div className="relative mb-4">
        {/* Ombre principale de la porte */}
        <div className={`absolute top-2 left-2 ${width} ${height} bg-black/50 rounded-lg transform rotate-3 blur-lg`}></div>
        
        {/* Porte principale en bois */}
        <div className={`relative ${width} ${height} bg-gradient-to-br from-amber-100 via-amber-200 to-amber-300 rounded-lg shadow-2xl transform -rotate-2 border-2 border-amber-400`}>
          {/* Cadre intérieur de la porte */}
          <div className="absolute inset-2 border-2 border-amber-500 rounded-md"></div>
          
          {/* Poignée de porte ronde */}
          <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
            <div className="w-2 h-2 bg-gradient-to-br from-yellow-700 to-yellow-900 rounded-full shadow-lg border border-yellow-800"></div>
          </div>
          
          {/* Texture et grain du bois */}
          <div className="absolute inset-0 opacity-40 rounded-lg overflow-hidden">
            {/* Lignes horizontales du grain de bois */}
            <div className="absolute top-1/4 left-1 right-1 h-px bg-amber-600 opacity-60"></div>
            <div className="absolute top-1/2 left-1 right-1 h-px bg-amber-600 opacity-60"></div>
            <div className="absolute top-3/4 left-1 right-1 h-px bg-amber-600 opacity-60"></div>
            
            {/* Gradient de texture */}
            <div className="h-full w-full bg-gradient-to-r from-transparent via-amber-400 to-transparent opacity-50"></div>
            <div className="absolute top-0 h-full w-full bg-gradient-to-b from-transparent via-amber-400 to-transparent opacity-30"></div>
          </div>
          
          {/* Effet de brillance sur le bois */}
          <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-lg"></div>
          
          {/* Reflets sur les bords */}
          <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-white/40 to-transparent rounded-t-lg"></div>
          <div className="absolute top-0 left-0 h-full w-1 bg-gradient-to-b from-transparent via-white/40 to-transparent rounded-l-lg"></div>
        </div>
        
        {/* Effets 3D supplémentaires */}
        <div className={`absolute -top-1 -right-1 ${width} ${height} border-2 border-amber-500 rounded-lg opacity-60 transform rotate-1`}></div>
        <div className={`absolute -bottom-1 -left-1 ${width} ${height} border border-amber-600 rounded-lg opacity-40 transform -rotate-1`}></div>
      </div>
      
      {/* Texte DOORLY en 3D */}
      {showText && (
        <div className="relative">
          {/* Ombre du texte */}
          <h1 className={`absolute top-1 left-1 ${textSize} font-black text-amber-900/50 blur-sm`}>
            DOORLY
          </h1>
          {/* Texte principal */}
          <h1 className={`relative ${textSize} font-black ${textColor} drop-shadow-2xl`}>
            DOORLY
          </h1>
          {/* Effet brillant sur le texte */}
          <div className={`absolute inset-0 ${textSize} font-black bg-gradient-to-t from-transparent to-white/20 bg-clip-text text-transparent`}>
            DOORLY
          </div>
        </div>
      )}
    </div>
  );
};

export default DoorlyLogo;
