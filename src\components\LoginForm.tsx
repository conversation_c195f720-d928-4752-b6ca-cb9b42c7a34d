import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { DoorOpen as Door, User, Phone, Lock, AlertCircle } from 'lucide-react';

const LoginForm: React.FC = () => {
  const [role, setRole] = useState<'admin' | 'client'>('client');
  const [identifier, setIdentifier] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const { login } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      const success = await login(identifier, password, role);
      if (!success) {
        setError('Identifiants incorrects. Veuillez réessayer.');
      }
    } catch (err) {
      setError('Une erreur est survenue. Veuillez réessayer.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-orange-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Logo */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-24 h-24 bg-white rounded-3xl mb-6 shadow-2xl border border-orange-100 overflow-hidden">
            <img 
              src="/278426439_103701565654071_785914601533428886_n copy.jpg" 
              alt="Doorly Logo" 
              className="w-full h-full object-cover"
            />
          </div>
          <h1 className="text-5xl font-bold bg-gradient-to-r from-orange-600 to-orange-800 bg-clip-text text-transparent mb-2">DOORLY</h1>
          <p className="text-gray-600 font-medium">Portes & Fenêtres Premium</p>
        </div>

        {/* Login Form */}
        <div className="bg-white/95 backdrop-blur-xl rounded-3xl p-8 shadow-2xl border border-orange-100">
          <div className="flex mb-6 bg-gray-50 rounded-2xl p-1">
            <button
              type="button"
              onClick={() => setRole('client')}
              className={`flex-1 py-3 px-4 rounded-xl font-semibold transition-all duration-300 ${
                role === 'client'
                  ? 'bg-gradient-to-r from-orange-500 to-orange-600 text-white shadow-lg transform scale-105'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              <User className="w-4 h-4 inline mr-2" />
              Client
            </button>
            <button
              type="button"
              onClick={() => setRole('admin')}
              className={`flex-1 py-3 px-4 rounded-xl font-semibold transition-all duration-300 ${
                role === 'admin'
                  ? 'bg-gradient-to-r from-gray-800 to-black text-white shadow-lg transform scale-105'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              <Lock className="w-4 h-4 inline mr-2" />
              Admin
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label className="block text-sm font-semibold text-gray-800 mb-2">
                {role === 'admin' ? 'Nom d\'utilisateur' : 'Numéro de téléphone'}
              </label>
              <div className="relative">
                {role === 'admin' ? (
                  <User className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-orange-500" />
                ) : (
                  <Phone className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-orange-500" />
                )}
                <input
                  type={role === 'admin' ? 'text' : 'tel'}
                  value={identifier}
                  onChange={(e) => setIdentifier(e.target.value)}
                  className="w-full pl-12 pr-4 py-4 bg-gray-50 border-2 border-gray-200 rounded-2xl text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-300"
                  placeholder={role === 'admin' ? 'admin' : '+212 6XX XXX XXX'}
                  required
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-semibold text-gray-800 mb-2">
                Mot de passe
              </label>
              <div className="relative">
                <Lock className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-orange-500" />
                <input
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full pl-12 pr-4 py-4 bg-gray-50 border-2 border-gray-200 rounded-2xl text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-300"
                  placeholder="••••••••"
                  required
                />
              </div>
            </div>

            {error && (
              <div className="flex items-center space-x-2 text-red-600 bg-red-50 p-4 rounded-2xl border border-red-200">
                <AlertCircle className="w-5 h-5" />
                <span className="text-sm font-medium">{error}</span>
              </div>
            )}

            <button
              type="submit"
              disabled={loading}
              className={`w-full py-4 rounded-2xl font-bold text-white transition-all duration-300 transform hover:scale-105 shadow-xl ${
                role === 'admin'
                  ? 'bg-gradient-to-r from-gray-800 to-black hover:from-gray-900 hover:to-gray-800'
                  : 'bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700'
              } disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none`}
            >
              {loading ? (
                <div className="flex items-center justify-center">
                  <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2"></div>
                  Connexion...
                </div>
              ) : (
                'Se connecter'
              )}
            </button>
          </form>

          <div className="mt-6 text-center text-sm text-gray-500">
            {role === 'admin' ? (
              <p>Admin: admin / admin123</p>
            ) : (
              <p>Client: Tout numéro (8+ chiffres) / Tout mot de passe (6+ caractères)</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginForm;