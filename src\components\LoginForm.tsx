import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import {
  DoorOpen as Door,
  User,
  Phone,
  Lock,
  AlertCircle,
  QrCode,
  Smartphone,
  Eye,
  EyeOff,
  ArrowRight,
  Sparkles,
  Shield,
  Zap,
  Star,
  Wifi,
  Globe
} from 'lucide-react';

const LoginForm: React.FC = () => {
  const [role, setRole] = useState<'admin' | 'client'>('client');
  const [identifier, setIdentifier] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showQR, setShowQR] = useState(false);
  const [animationStep, setAnimationStep] = useState(0);
  const { login } = useAuth();

  // Animation d'entrée
  useEffect(() => {
    const timer = setTimeout(() => setAnimationStep(1), 100);
    return () => clearTimeout(timer);
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      const success = await login(identifier, password, role);
      if (!success) {
        setError('Identifiants incorrects. Veuillez réessayer.');
      }
    } catch (err) {
      setError('Une erreur est survenue. Veuillez réessayer.');
    } finally {
      setLoading(false);
    }
  };

  // Composant QR Code simple (sans dépendance externe)
  const QRCodeComponent = () => (
    <div className="bg-white p-6 rounded-3xl shadow-2xl border border-gray-100">
      <div className="text-center mb-4">
        <QrCode className="w-8 h-8 text-orange-500 mx-auto mb-2" />
        <h3 className="font-bold text-gray-900">Connexion rapide</h3>
        <p className="text-sm text-gray-600">Scannez avec votre téléphone</p>
      </div>

      {/* QR Code simulé avec CSS */}
      <div className="w-48 h-48 mx-auto bg-white border-2 border-gray-200 rounded-2xl p-4 relative overflow-hidden">
        <div className="absolute inset-4 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg opacity-10"></div>

        {/* Pattern QR simulé */}
        <div className="grid grid-cols-8 gap-1 h-full">
          {Array.from({ length: 64 }, (_, i) => (
            <div
              key={i}
              className={`rounded-sm ${
                Math.random() > 0.5 ? 'bg-gray-900' : 'bg-transparent'
              }`}
            />
          ))}
        </div>

        {/* Logo au centre */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="bg-white rounded-xl p-2 shadow-lg border border-gray-200">
            <Door className="w-6 h-6 text-orange-500" />
          </div>
        </div>
      </div>

      <div className="mt-4 text-center">
        <p className="text-xs text-gray-500">
          Ouvrez l'app Doorly sur votre téléphone
        </p>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-orange-500/10 rounded-full blur-3xl animate-pulse-slow"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse-slow" style={{ animationDelay: '1s' }}></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl animate-pulse-slow" style={{ animationDelay: '2s' }}></div>
      </div>

      {/* Floating particles */}
      <div className="absolute inset-0 overflow-hidden">
        {Array.from({ length: 20 }, (_, i) => (
          <div
            key={i}
            className="absolute w-2 h-2 bg-white/20 rounded-full animate-bounce-gentle"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${3 + Math.random() * 2}s`,
            }}
          />
        ))}
      </div>

      <div className="relative z-10 min-h-screen flex items-center justify-center p-4">
        <div className={`w-full max-w-6xl grid lg:grid-cols-2 gap-8 items-center transition-all duration-1000 ${animationStep ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>

          {/* Left side - Branding & QR */}
          <div className="text-center lg:text-left space-y-8">
            <div className="space-y-6">
              <div className="inline-flex items-center space-x-3 bg-white/10 backdrop-blur-xl rounded-full px-6 py-3 border border-white/20">
                <Sparkles className="w-5 h-5 text-orange-400" />
                <span className="text-white font-medium">Nouvelle expérience</span>
              </div>

              <div>
                <h1 className="text-6xl lg:text-7xl font-black text-white mb-4 leading-tight">
                  <span className="bg-gradient-to-r from-orange-400 to-pink-400 bg-clip-text text-transparent">
                    Doorly
                  </span>
                </h1>
                <p className="text-xl text-gray-300 mb-6 leading-relaxed">
                  L'avenir des portes et fenêtres premium.<br />
                  <span className="text-orange-400 font-semibold">Design. Innovation. Excellence.</span>
                </p>
              </div>

              <div className="flex flex-wrap gap-4 justify-center lg:justify-start">
                <div className="flex items-center space-x-2 bg-white/5 backdrop-blur-xl rounded-2xl px-4 py-2 border border-white/10">
                  <Shield className="w-5 h-5 text-green-400" />
                  <span className="text-white text-sm font-medium">Sécurisé</span>
                </div>
                <div className="flex items-center space-x-2 bg-white/5 backdrop-blur-xl rounded-2xl px-4 py-2 border border-white/10">
                  <Zap className="w-5 h-5 text-yellow-400" />
                  <span className="text-white text-sm font-medium">Ultra-rapide</span>
                </div>
                <div className="flex items-center space-x-2 bg-white/5 backdrop-blur-xl rounded-2xl px-4 py-2 border border-white/10">
                  <Smartphone className="w-5 h-5 text-blue-400" />
                  <span className="text-white text-sm font-medium">Mobile-first</span>
                </div>
              </div>
            </div>

            {/* QR Code Toggle Button */}
            <div className="flex justify-center lg:justify-start">
              <button
                onClick={() => setShowQR(!showQR)}
                className="group flex items-center space-x-3 bg-gradient-to-r from-orange-500 to-pink-500 hover:from-orange-600 hover:to-pink-600 text-white px-6 py-3 rounded-2xl font-semibold transition-all duration-300 transform hover:scale-105 shadow-2xl shadow-orange-500/25"
              >
                <QrCode className="w-5 h-5 group-hover:rotate-12 transition-transform duration-300" />
                <span>{showQR ? 'Masquer QR' : 'Connexion QR'}</span>
              </button>
            </div>

            {/* QR Code Section */}
            {showQR && (
              <div className="animate-fade-in-scale">
                <QRCodeComponent />
              </div>
            )}
          </div>

          {/* Right side - Login Form */}
          <div className="w-full max-w-md mx-auto">
            <div className="bg-white/10 backdrop-blur-2xl rounded-3xl p-8 border border-white/20 shadow-2xl">
              <div className="text-center mb-8">
                <div className="w-16 h-16 bg-gradient-to-r from-orange-500 to-pink-500 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-2xl shadow-orange-500/25">
                  <Door className="w-8 h-8 text-white" />
                </div>
                <h2 className="text-2xl font-bold text-white mb-2">Connexion</h2>
                <p className="text-gray-300">Accédez à votre espace</p>
              </div>

              {/* Role Selection */}
              <div className="flex mb-6 bg-white/5 backdrop-blur-xl rounded-2xl p-1 border border-white/10">
                <button
                  type="button"
                  onClick={() => setRole('client')}
                  className={`flex-1 py-3 px-4 rounded-xl font-semibold transition-all duration-300 ${
                    role === 'client'
                      ? 'bg-gradient-to-r from-orange-500 to-pink-500 text-white shadow-lg transform scale-105'
                      : 'text-gray-300 hover:text-white hover:bg-white/10'
                  }`}
                >
                  <User className="w-4 h-4 inline mr-2" />
                  Client
                </button>
                <button
                  type="button"
                  onClick={() => setRole('admin')}
                  className={`flex-1 py-3 px-4 rounded-xl font-semibold transition-all duration-300 ${
                    role === 'admin'
                      ? 'bg-gradient-to-r from-purple-500 to-indigo-500 text-white shadow-lg transform scale-105'
                      : 'text-gray-300 hover:text-white hover:bg-white/10'
                  }`}
                >
                  <Lock className="w-4 h-4 inline mr-2" />
                  Admin
                </button>
              </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label className="block text-sm font-semibold text-gray-800 mb-2">
                {role === 'admin' ? 'Nom d\'utilisateur' : 'Numéro de téléphone'}
              </label>
              <div className="relative">
                {role === 'admin' ? (
                  <User className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-orange-500" />
                ) : (
                  <Phone className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-orange-500" />
                )}
                <input
                  type={role === 'admin' ? 'text' : 'tel'}
                  value={identifier}
                  onChange={(e) => setIdentifier(e.target.value)}
                  className="w-full pl-12 pr-4 py-4 bg-gray-50 border-2 border-gray-200 rounded-2xl text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-300"
                  placeholder={role === 'admin' ? 'admin' : '+212 6XX XXX XXX'}
                  required
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-semibold text-gray-800 mb-2">
                Mot de passe
              </label>
              <div className="relative">
                <Lock className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-orange-500" />
                <input
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full pl-12 pr-4 py-4 bg-gray-50 border-2 border-gray-200 rounded-2xl text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-300"
                  placeholder="••••••••"
                  required
                />
              </div>
            </div>

            {error && (
              <div className="flex items-center space-x-2 text-red-600 bg-red-50 p-4 rounded-2xl border border-red-200">
                <AlertCircle className="w-5 h-5" />
                <span className="text-sm font-medium">{error}</span>
              </div>
            )}

            <button
              type="submit"
              disabled={loading}
              className={`w-full py-4 rounded-2xl font-bold text-white transition-all duration-300 transform hover:scale-105 shadow-xl ${
                role === 'admin'
                  ? 'bg-gradient-to-r from-gray-800 to-black hover:from-gray-900 hover:to-gray-800'
                  : 'bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700'
              } disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none`}
            >
              {loading ? (
                <div className="flex items-center justify-center">
                  <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2"></div>
                  Connexion...
                </div>
              ) : (
                'Se connecter'
              )}
            </button>
          </form>

          <div className="mt-6 text-center text-sm text-gray-500">
            {role === 'admin' ? (
              <p>Admin: admin / admin123</p>
            ) : (
              <p>Client: Tout numéro (8+ chiffres) / Tout mot de passe (6+ caractères)</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginForm;