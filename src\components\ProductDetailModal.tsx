import React from 'react';
import { Product } from '../types';
import { X, Package, Palette, Ruler, Tag, Info, ShoppingCart, Calendar } from 'lucide-react';

interface ProductDetailModalProps {
  product: Product;
  onClose: () => void;
  onBuy: (product: Product) => void;
  onReserve: (product: Product) => void;
}

const ProductDetailModal: React.FC<ProductDetailModalProps> = ({ 
  product, 
  onClose, 
  onBuy, 
  onReserve 
}) => {
  const getCategoryName = (category: string) => {
    switch (category) {
      case 'doors': return 'Portes';
      case 'windows': return 'Fenêtres';
      case 'aluminum': return 'Aluminium';
      case 'carpentry': return 'Menuiserie';
      default: return category;
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-3xl max-w-4xl w-full shadow-2xl max-h-[90vh] overflow-y-auto border-2 border-orange-200">
        <div className="relative">
          {/* Header */}
          <div className="flex justify-between items-center p-6 border-b border-orange-100">
            <h2 className="text-2xl font-bold text-gray-900">Détails du Produit</h2>
            <button
              onClick={onClose}
              className="p-2 hover:bg-orange-100 rounded-full transition-colors duration-200"
            >
              <X className="w-6 h-6 text-gray-600" />
            </button>
          </div>

          {/* Content */}
          <div className="p-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Image Section */}
              <div className="space-y-4">
                <div className="relative overflow-hidden rounded-2xl">
                  <img
                    src={product.image}
                    alt={product.name}
                    className="w-full h-80 object-cover"
                  />
                  <div className="absolute top-4 left-4 px-3 py-1 rounded-full text-xs font-bold text-white bg-gradient-to-r from-gray-800 to-black">
                    {getCategoryName(product.category)}
                  </div>
                  <div className="absolute top-4 right-4 bg-white/95 backdrop-blur-sm rounded-full px-4 py-2 border border-orange-200">
                    <span className="text-orange-600 font-bold text-xl">{product.price}€</span>
                  </div>
                </div>
              </div>

              {/* Details Section */}
              <div className="space-y-6">
                <div>
                  <h1 className="text-3xl font-bold text-gray-900 mb-2">{product.name}</h1>
                  <p className="text-gray-600 leading-relaxed">{product.description}</p>
                </div>

                {/* Specifications */}
                <div className="bg-orange-50 rounded-2xl p-6 border border-orange-200">
                  <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center">
                    <Info className="w-5 h-5 mr-2 text-orange-600" />
                    Spécifications
                  </h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-orange-200 rounded-full flex items-center justify-center">
                        <Ruler className="w-5 h-5 text-orange-700" />
                      </div>
                      <div>
                        <p className="text-sm text-gray-600 font-medium">Dimensions</p>
                        <p className="font-bold text-gray-900">{product.size}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-orange-200 rounded-full flex items-center justify-center">
                        <Palette className="w-5 h-5 text-orange-700" />
                      </div>
                      <div>
                        <p className="text-sm text-gray-600 font-medium">Couleur</p>
                        <p className="font-bold text-gray-900">{product.color}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-orange-200 rounded-full flex items-center justify-center">
                        <Package className="w-5 h-5 text-orange-700" />
                      </div>
                      <div>
                        <p className="text-sm text-gray-600 font-medium">Stock</p>
                        <p className={`font-bold ${product.stock > 10 ? 'text-green-600' : product.stock > 0 ? 'text-orange-600' : 'text-red-600'}`}>
                          {product.stock > 0 ? `${product.stock} disponible` : 'Rupture de stock'}
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-orange-200 rounded-full flex items-center justify-center">
                        <Tag className="w-5 h-5 text-orange-700" />
                      </div>
                      <div>
                        <p className="text-sm text-gray-600 font-medium">Prix</p>
                        <p className="font-bold text-2xl text-orange-600">{product.price}€</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Features */}
                <div className="bg-gradient-to-r from-orange-50 to-orange-100 rounded-2xl p-6 border border-orange-200">
                  <h3 className="text-lg font-bold text-gray-900 mb-4">Caractéristiques Premium</h3>
                  <ul className="space-y-2 text-gray-700">
                    <li className="flex items-center">
                      <div className="w-2 h-2 bg-orange-600 rounded-full mr-3"></div>
                      <span className="font-medium">Matériaux de haute qualité</span>
                    </li>
                    <li className="flex items-center">
                      <div className="w-2 h-2 bg-orange-600 rounded-full mr-3"></div>
                      <span className="font-medium">Installation professionnelle incluse</span>
                    </li>
                    <li className="flex items-center">
                      <div className="w-2 h-2 bg-orange-600 rounded-full mr-3"></div>
                      <span className="font-medium">Garantie 10 ans</span>
                    </li>
                    <li className="flex items-center">
                      <div className="w-2 h-2 bg-orange-600 rounded-full mr-3"></div>
                      <span className="font-medium">Isolation thermique optimale</span>
                    </li>
                  </ul>
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-4">
                  <button
                    onClick={() => onBuy(product)}
                    disabled={product.stock === 0}
                    className="flex-1 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white py-4 px-6 rounded-2xl font-bold transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center space-x-2 shadow-xl"
                  >
                    <ShoppingCart className="w-5 h-5" />
                    <span>Acheter Maintenant</span>
                  </button>
                  
                  <button
                    onClick={() => onReserve(product)}
                    disabled={product.stock === 0}
                    className="flex-1 bg-gradient-to-r from-gray-800 to-black hover:from-gray-900 hover:to-gray-800 text-white py-4 px-6 rounded-2xl font-bold transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center space-x-2 shadow-xl"
                  >
                    <Calendar className="w-5 h-5" />
                    <span>Réserver</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductDetailModal;