import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { products as initialProducts } from '../data/products';
import { Product, Order } from '../types';
import {
  Plus,
  Edit,
  Trash2,
  Package,
  LogOut,
  DoorOpen as Door,
  User,
  Search,
  Filter,
  Save,
  X,
  BarChart3,
  TrendingUp,
  AlertTriangle,
  Download,
  Database,
  Users,
  ShoppingCart,
  Calendar,
  Eye,
  Settings,
  Bell,
  Menu,
  Home,
  FileText,
  PieChart,
  Activity,
  Zap,
  Shield,
  Star,
  CheckCircle,
  Clock,
  DollarSign
} from 'lucide-react';

const ModernAdminDashboard: React.FC = () => {
  const { user, logout } = useAuth();
  const [products, setProducts] = useState<Product[]>(initialProducts);
  const [orders, setOrders] = useState<Order[]>([
    // Données de démonstration
    {
      id: '1',
      clientName: '<PERSON>',
      clientPhone: '+212 661234567',
      product: initialProducts[0],
      quantity: 2,
      totalPrice: 2400,
      status: 'purchased',
      date: '2024-01-15',
      paymentMethod: 'Carte bancaire'
    },
    {
      id: '2',
      clientName: 'Fatima Zahra',
      clientPhone: '+212 662345678',
      product: initialProducts[1],
      quantity: 1,
      totalPrice: 800,
      status: 'reserved',
      date: '2024-01-14',
      paymentMethod: 'Espèces'
    }
  ]);
  const [clients] = useState([
    { id: '1', name: 'Ahmed Benali', phone: '+212 661234567', email: '<EMAIL>', orders: 3, totalSpent: 5200 },
    { id: '2', name: 'Fatima Zahra', phone: '+212 662345678', email: '<EMAIL>', orders: 1, totalSpent: 800 },
    { id: '3', name: 'Mohamed Alami', phone: '+212 663456789', email: '<EMAIL>', orders: 2, totalSpent: 3400 }
  ]);

  const [activeTab, setActiveTab] = useState('dashboard');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [formData, setFormData] = useState<Partial<Product>>({});
  const [showMobileMenu, setShowMobileMenu] = useState(false);

  const categories = [
    { id: 'all', name: 'Tous les produits', icon: '🏠' },
    { id: 'doors', name: 'Portes', icon: '🚪' },
    { id: 'windows', name: 'Fenêtres', icon: '🪟' },
    { id: 'aluminum', name: 'Aluminium', icon: '⚡' },
    { id: 'carpentry', name: 'Menuiserie', icon: '🔨' }
  ];

  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const stats = {
    totalProducts: products.length,
    totalOrders: orders.length,
    totalClients: clients.length,
    totalRevenue: orders.reduce((sum, order) => sum + order.totalPrice, 0),
    pendingOrders: orders.filter(order => order.status === 'reserved').length,
    completedOrders: orders.filter(order => order.status === 'purchased').length,
    lowStockProducts: products.filter(product => product.stock < 5).length
  };

  const handleAddProduct = () => {
    setFormData({
      name: '',
      price: 0,
      size: '',
      color: '',
      stock: 0,
      category: 'doors',
      image: 'https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg?auto=compress&cs=tinysrgb&w=400',
      description: ''
    });
    setEditingProduct(null);
    setShowAddModal(true);
  };

  const handleEditProduct = (product: Product) => {
    setFormData(product);
    setEditingProduct(product);
    setShowAddModal(true);
  };

  const handleDeleteProduct = (productId: string) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce produit ?')) {
      setProducts(products.filter(p => p.id !== productId));
    }
  };

  const handleSaveProduct = () => {
    if (editingProduct) {
      setProducts(products.map(p => p.id === editingProduct.id ? { ...formData as Product } : p));
    } else {
      const newProduct: Product = {
        ...formData as Product,
        id: Date.now().toString()
      };
      setProducts([...products, newProduct]);
    }
    setShowAddModal(false);
    setFormData({});
    setEditingProduct(null);
  };

  const renderDashboard = () => (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-3xl p-6 shadow-xl border border-orange-100 hover:shadow-2xl transition-all duration-300 hover:scale-105">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm font-medium">Total Produits</p>
              <p className="text-3xl font-black text-gray-900">{stats.totalProducts}</p>
              <p className="text-green-600 text-xs mt-1 flex items-center">
                <TrendingUp className="w-3 h-3 mr-1" />
                +12% ce mois
              </p>
            </div>
            <div className="w-16 h-16 bg-gradient-to-r from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center">
              <Package className="w-8 h-8 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-3xl p-6 shadow-xl border border-orange-100 hover:shadow-2xl transition-all duration-300 hover:scale-105">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm font-medium">Commandes</p>
              <p className="text-3xl font-black text-gray-900">{stats.totalOrders}</p>
              <p className="text-green-600 text-xs mt-1 flex items-center">
                <TrendingUp className="w-3 h-3 mr-1" />
                +8% ce mois
              </p>
            </div>
            <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center">
              <ShoppingCart className="w-8 h-8 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-3xl p-6 shadow-xl border border-orange-100 hover:shadow-2xl transition-all duration-300 hover:scale-105">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm font-medium">Clients</p>
              <p className="text-3xl font-black text-gray-900">{stats.totalClients}</p>
              <p className="text-green-600 text-xs mt-1 flex items-center">
                <TrendingUp className="w-3 h-3 mr-1" />
                +15% ce mois
              </p>
            </div>
            <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-green-600 rounded-2xl flex items-center justify-center">
              <Users className="w-8 h-8 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-3xl p-6 shadow-xl border border-orange-100 hover:shadow-2xl transition-all duration-300 hover:scale-105">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm font-medium">Chiffre d'affaires</p>
              <p className="text-3xl font-black text-gray-900">{stats.totalRevenue.toLocaleString()} DH</p>
              <p className="text-green-600 text-xs mt-1 flex items-center">
                <TrendingUp className="w-3 h-3 mr-1" />
                +25% ce mois
              </p>
            </div>
            <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center">
              <DollarSign className="w-8 h-8 text-white" />
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-3xl p-6 shadow-xl border border-orange-100">
          <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center">
            <Zap className="w-5 h-5 text-orange-500 mr-2" />
            Actions rapides
          </h3>
          <div className="space-y-3">
            <button
              onClick={handleAddProduct}
              className="w-full bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-bold py-3 px-4 rounded-2xl transition-all duration-300 transform hover:scale-105 flex items-center justify-center space-x-2"
            >
              <Plus className="w-4 h-4" />
              <span>Ajouter un produit</span>
            </button>
            <button
              onClick={() => setActiveTab('database')}
              className="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-bold py-3 px-4 rounded-2xl transition-all duration-300 transform hover:scale-105 flex items-center justify-center space-x-2"
            >
              <Database className="w-4 h-4" />
              <span>Base de données</span>
            </button>
            <button
              onClick={() => setActiveTab('reports')}
              className="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-bold py-3 px-4 rounded-2xl transition-all duration-300 transform hover:scale-105 flex items-center justify-center space-x-2"
            >
              <BarChart3 className="w-4 h-4" />
              <span>Rapports</span>
            </button>
          </div>
        </div>

        <div className="bg-white rounded-3xl p-6 shadow-xl border border-orange-100">
          <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center">
            <Activity className="w-5 h-5 text-blue-500 mr-2" />
            Activité récente
          </h3>
          <div className="space-y-3">
            {orders.slice(0, 3).map((order) => (
              <div key={order.id} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-2xl">
                <div className={`w-3 h-3 rounded-full ${
                  order.status === 'purchased' ? 'bg-green-500' : 'bg-orange-500'
                }`}></div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">{order.clientName}</p>
                  <p className="text-xs text-gray-600">{order.product.name}</p>
                </div>
                <span className="text-sm font-bold text-orange-600">{order.totalPrice.toLocaleString()} DH</span>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white rounded-3xl p-6 shadow-xl border border-orange-100">
          <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center">
            <AlertTriangle className="w-5 h-5 text-red-500 mr-2" />
            Alertes stock
          </h3>
          <div className="space-y-3">
            {products.filter(p => p.stock < 5).slice(0, 3).map((product) => (
              <div key={product.id} className="flex items-center space-x-3 p-3 bg-red-50 rounded-2xl border border-red-200">
                <AlertTriangle className="w-4 h-4 text-red-500" />
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">{product.name}</p>
                  <p className="text-xs text-red-600">Stock: {product.stock}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Charts placeholder */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-3xl p-6 shadow-xl border border-orange-100">
          <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center">
            <PieChart className="w-5 h-5 text-purple-500 mr-2" />
            Ventes par catégorie
          </h3>
          <div className="h-64 bg-gray-50 rounded-2xl flex items-center justify-center">
            <p className="text-gray-500">Graphique des ventes par catégorie</p>
          </div>
        </div>

        <div className="bg-white rounded-3xl p-6 shadow-xl border border-orange-100">
          <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center">
            <BarChart3 className="w-5 h-5 text-green-500 mr-2" />
            Évolution des ventes
          </h3>
          <div className="h-64 bg-gray-50 rounded-2xl flex items-center justify-center">
            <p className="text-gray-500">Graphique d'évolution des ventes</p>
          </div>
        </div>
      </div>
    </div>
  );

  const renderProducts = () => (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
        <h2 className="text-2xl font-bold text-gray-900">Gestion des produits</h2>
        <button
          onClick={handleAddProduct}
          className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-bold py-3 px-6 rounded-2xl transition-all duration-300 transform hover:scale-105 flex items-center space-x-2 shadow-lg"
        >
          <Plus className="w-5 h-5" />
          <span>Ajouter un produit</span>
        </button>
      </div>

      {/* Filtres */}
      <div className="bg-white rounded-3xl p-6 shadow-xl border border-orange-100">
        <div className="flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="text"
                placeholder="Rechercher des produits..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-12 pr-4 py-3 bg-gray-50 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-orange-500 transition-all duration-300"
              />
            </div>
          </div>
          <div className="flex space-x-2 overflow-x-auto">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`flex items-center space-x-2 px-4 py-3 rounded-2xl font-medium transition-all duration-300 whitespace-nowrap ${
                  selectedCategory === category.id
                    ? 'bg-gradient-to-r from-orange-500 to-orange-600 text-white shadow-lg'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                <span>{category.icon}</span>
                <span>{category.name}</span>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Liste des produits */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {filteredProducts.map((product) => (
          <div key={product.id} className="bg-white rounded-3xl p-6 shadow-xl border border-orange-100 hover:shadow-2xl transition-all duration-300 hover:scale-105">
            <div className="relative mb-4">
              <img
                src={product.image}
                alt={product.name}
                className="w-full h-48 object-cover rounded-2xl"
              />
              <div className="absolute top-3 right-3 flex space-x-2">
                <button
                  onClick={() => handleEditProduct(product)}
                  className="p-2 bg-white/90 backdrop-blur-xl rounded-full hover:bg-white transition-all duration-300 shadow-lg"
                >
                  <Edit className="w-4 h-4 text-blue-600" />
                </button>
                <button
                  onClick={() => handleDeleteProduct(product.id)}
                  className="p-2 bg-white/90 backdrop-blur-xl rounded-full hover:bg-white transition-all duration-300 shadow-lg"
                >
                  <Trash2 className="w-4 h-4 text-red-600" />
                </button>
              </div>
              <div className="absolute bottom-3 left-3">
                <span className={`px-3 py-1 rounded-full text-xs font-bold ${
                  product.stock > 10
                    ? 'bg-green-500/90 text-white'
                    : product.stock > 0
                      ? 'bg-orange-500/90 text-white'
                      : 'bg-red-500/90 text-white'
                }`}>
                  {product.stock > 0 ? `${product.stock} en stock` : 'Rupture'}
                </span>
              </div>
            </div>

            <div className="space-y-3">
              <div>
                <h3 className="text-lg font-bold text-gray-900">{product.name}</h3>
                <p className="text-gray-600 text-sm">{product.description}</p>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <p className="text-2xl font-black text-orange-600">{product.price.toLocaleString()} DH</p>
                  <div className="flex items-center space-x-2 text-xs text-gray-500">
                    <span>{product.size}</span>
                    <span>•</span>
                    <span>{product.color}</span>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-900">{categories.find(c => c.id === product.category)?.name}</p>
                  <div className="flex items-center space-x-1">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-3 h-3 text-yellow-400 fill-current" />
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {filteredProducts.length === 0 && (
        <div className="text-center py-12">
          <div className="w-24 h-24 bg-gray-100 rounded-3xl flex items-center justify-center mx-auto mb-4">
            <Package className="w-12 h-12 text-gray-400" />
          </div>
          <h3 className="text-xl font-bold text-gray-900 mb-2">Aucun produit trouvé</h3>
          <p className="text-gray-600">Essayez de modifier vos critères de recherche</p>
        </div>
      )}
    </div>
  );

  const renderOrders = () => (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold text-gray-900">Gestion des commandes</h2>

      <div className="bg-white rounded-3xl shadow-xl border border-orange-100 overflow-hidden">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-bold text-gray-900">Liste des commandes</h3>
            <div className="flex space-x-2">
              <span className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
                {stats.completedOrders} Payées
              </span>
              <span className="px-3 py-1 bg-orange-100 text-orange-800 rounded-full text-sm font-medium">
                {stats.pendingOrders} En attente
              </span>
            </div>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-4 text-left text-xs font-bold text-gray-900 uppercase tracking-wider">Client</th>
                <th className="px-6 py-4 text-left text-xs font-bold text-gray-900 uppercase tracking-wider">Produit</th>
                <th className="px-6 py-4 text-left text-xs font-bold text-gray-900 uppercase tracking-wider">Quantité</th>
                <th className="px-6 py-4 text-left text-xs font-bold text-gray-900 uppercase tracking-wider">Total</th>
                <th className="px-6 py-4 text-left text-xs font-bold text-gray-900 uppercase tracking-wider">Statut</th>
                <th className="px-6 py-4 text-left text-xs font-bold text-gray-900 uppercase tracking-wider">Date</th>
                <th className="px-6 py-4 text-left text-xs font-bold text-gray-900 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {orders.map((order) => (
                <tr key={order.id} className="hover:bg-gray-50 transition-colors duration-200">
                  <td className="px-6 py-4">
                    <div>
                      <p className="font-medium text-gray-900">{order.clientName}</p>
                      <p className="text-sm text-gray-600">{order.clientPhone}</p>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center space-x-3">
                      <img src={order.product.image} alt={order.product.name} className="w-12 h-12 object-cover rounded-xl" />
                      <div>
                        <p className="font-medium text-gray-900">{order.product.name}</p>
                        <p className="text-sm text-gray-600">{order.product.size}</p>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 text-gray-900 font-medium">{order.quantity}</td>
                  <td className="px-6 py-4 text-orange-600 font-bold">{order.totalPrice.toLocaleString()} DH</td>
                  <td className="px-6 py-4">
                    <span className={`px-3 py-1 rounded-full text-xs font-bold ${
                      order.status === 'purchased'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-orange-100 text-orange-800'
                    }`}>
                      {order.status === 'purchased' ? 'Payé' : 'Réservé'}
                    </span>
                  </td>
                  <td className="px-6 py-4 text-gray-600">{new Date(order.date).toLocaleDateString('fr-FR')}</td>
                  <td className="px-6 py-4">
                    <div className="flex space-x-2">
                      <button className="p-2 bg-blue-100 hover:bg-blue-200 rounded-xl transition-colors duration-200">
                        <Eye className="w-4 h-4 text-blue-600" />
                      </button>
                      <button className="p-2 bg-green-100 hover:bg-green-200 rounded-xl transition-colors duration-200">
                        <Download className="w-4 h-4 text-green-600" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  const renderClients = () => (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold text-gray-900">Gestion des clients</h2>

      <div className="bg-white rounded-3xl shadow-xl border border-orange-100 overflow-hidden">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-bold text-gray-900">Liste des clients</h3>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-4 text-left text-xs font-bold text-gray-900 uppercase tracking-wider">Client</th>
                <th className="px-6 py-4 text-left text-xs font-bold text-gray-900 uppercase tracking-wider">Contact</th>
                <th className="px-6 py-4 text-left text-xs font-bold text-gray-900 uppercase tracking-wider">Commandes</th>
                <th className="px-6 py-4 text-left text-xs font-bold text-gray-900 uppercase tracking-wider">Total dépensé</th>
                <th className="px-6 py-4 text-left text-xs font-bold text-gray-900 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {clients.map((client) => (
                <tr key={client.id} className="hover:bg-gray-50 transition-colors duration-200">
                  <td className="px-6 py-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-orange-500 to-orange-600 rounded-full flex items-center justify-center">
                        <User className="w-5 h-5 text-white" />
                      </div>
                      <p className="font-medium text-gray-900">{client.name}</p>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div>
                      <p className="text-gray-900">{client.phone}</p>
                      <p className="text-sm text-gray-600">{client.email}</p>
                    </div>
                  </td>
                  <td className="px-6 py-4 text-gray-900 font-medium">{client.orders}</td>
                  <td className="px-6 py-4 text-orange-600 font-bold">{client.totalSpent.toLocaleString()} DH</td>
                  <td className="px-6 py-4">
                    <div className="flex space-x-2">
                      <button className="p-2 bg-blue-100 hover:bg-blue-200 rounded-xl transition-colors duration-200">
                        <Eye className="w-4 h-4 text-blue-600" />
                      </button>
                      <button className="p-2 bg-green-100 hover:bg-green-200 rounded-xl transition-colors duration-200">
                        <FileText className="w-4 h-4 text-green-600" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-white via-orange-50 to-orange-100">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-xl border-b border-orange-200 sticky top-0 z-40">
        <div className="px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center shadow-lg">
                <Door className="w-7 h-7 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-black text-gray-900">Doorly Admin</h1>
                <p className="text-sm text-gray-600">Tableau de bord administrateur</p>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <button className="relative p-3 rounded-xl bg-white shadow-lg border border-orange-200 hover:shadow-xl transition-all duration-300">
                <Bell className="w-5 h-5 text-gray-600" />
                <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
              </button>

              <div className="flex items-center space-x-3 bg-white rounded-2xl px-4 py-2 shadow-lg border border-orange-200">
                <div className="w-8 h-8 bg-gradient-to-r from-gray-600 to-gray-700 rounded-xl flex items-center justify-center">
                  <User className="w-4 h-4 text-white" />
                </div>
                <div>
                  <p className="text-sm font-bold text-gray-900">{user?.name}</p>
                  <p className="text-xs text-gray-600">Administrateur</p>
                </div>
              </div>

              <button
                onClick={() => setShowMobileMenu(!showMobileMenu)}
                className="p-3 rounded-xl bg-white shadow-lg border border-orange-200 hover:shadow-xl transition-all duration-300 md:hidden"
              >
                {showMobileMenu ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="flex">
        {/* Sidebar Desktop */}
        <aside className="hidden md:block w-64 bg-white/80 backdrop-blur-xl border-r border-orange-200 min-h-screen">
          <nav className="p-6 space-y-2">
            <button
              onClick={() => setActiveTab('dashboard')}
              className={`w-full flex items-center space-x-3 px-4 py-3 rounded-2xl font-medium transition-all duration-300 ${
                activeTab === 'dashboard'
                  ? 'bg-gradient-to-r from-orange-500 to-orange-600 text-white shadow-lg'
                  : 'text-gray-600 hover:bg-orange-50 hover:text-orange-600'
              }`}
            >
              <Home className="w-5 h-5" />
              <span>Tableau de bord</span>
            </button>

            <button
              onClick={() => setActiveTab('products')}
              className={`w-full flex items-center space-x-3 px-4 py-3 rounded-2xl font-medium transition-all duration-300 ${
                activeTab === 'products'
                  ? 'bg-gradient-to-r from-orange-500 to-orange-600 text-white shadow-lg'
                  : 'text-gray-600 hover:bg-orange-50 hover:text-orange-600'
              }`}
            >
              <Package className="w-5 h-5" />
              <span>Produits</span>
            </button>

            <button
              onClick={() => setActiveTab('orders')}
              className={`w-full flex items-center space-x-3 px-4 py-3 rounded-2xl font-medium transition-all duration-300 ${
                activeTab === 'orders'
                  ? 'bg-gradient-to-r from-orange-500 to-orange-600 text-white shadow-lg'
                  : 'text-gray-600 hover:bg-orange-50 hover:text-orange-600'
              }`}
            >
              <ShoppingCart className="w-5 h-5" />
              <span>Commandes</span>
            </button>

            <button
              onClick={() => setActiveTab('clients')}
              className={`w-full flex items-center space-x-3 px-4 py-3 rounded-2xl font-medium transition-all duration-300 ${
                activeTab === 'clients'
                  ? 'bg-gradient-to-r from-orange-500 to-orange-600 text-white shadow-lg'
                  : 'text-gray-600 hover:bg-orange-50 hover:text-orange-600'
              }`}
            >
              <Users className="w-5 h-5" />
              <span>Clients</span>
            </button>

            <button
              onClick={() => setActiveTab('database')}
              className={`w-full flex items-center space-x-3 px-4 py-3 rounded-2xl font-medium transition-all duration-300 ${
                activeTab === 'database'
                  ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-lg'
                  : 'text-gray-600 hover:bg-blue-50 hover:text-blue-600'
              }`}
            >
              <Database className="w-5 h-5" />
              <span>Base de données</span>
            </button>

            <button
              onClick={() => setActiveTab('reports')}
              className={`w-full flex items-center space-x-3 px-4 py-3 rounded-2xl font-medium transition-all duration-300 ${
                activeTab === 'reports'
                  ? 'bg-gradient-to-r from-green-500 to-green-600 text-white shadow-lg'
                  : 'text-gray-600 hover:bg-green-50 hover:text-green-600'
              }`}
            >
              <BarChart3 className="w-5 h-5" />
              <span>Rapports</span>
            </button>

            <button
              onClick={() => setActiveTab('settings')}
              className={`w-full flex items-center space-x-3 px-4 py-3 rounded-2xl font-medium transition-all duration-300 ${
                activeTab === 'settings'
                  ? 'bg-gradient-to-r from-purple-500 to-purple-600 text-white shadow-lg'
                  : 'text-gray-600 hover:bg-purple-50 hover:text-purple-600'
              }`}
            >
              <Settings className="w-5 h-5" />
              <span>Paramètres</span>
            </button>

            <div className="pt-4 border-t border-orange-200">
              <button
                onClick={logout}
                className="w-full flex items-center space-x-3 px-4 py-3 rounded-2xl font-medium text-red-600 hover:bg-red-50 transition-all duration-300"
              >
                <LogOut className="w-5 h-5" />
                <span>Déconnexion</span>
              </button>
            </div>
          </nav>
        </aside>

        {/* Menu mobile */}
        {showMobileMenu && (
          <div className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm md:hidden">
            <div className="absolute right-0 top-0 h-full w-80 bg-white/95 backdrop-blur-xl border-l border-orange-200 p-6">
              <div className="flex items-center justify-between mb-8">
                <h2 className="text-xl font-bold text-gray-900">Menu</h2>
                <button
                  onClick={() => setShowMobileMenu(false)}
                  className="p-2 rounded-xl bg-gray-100 hover:bg-gray-200 transition-colors duration-200"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>

              <nav className="space-y-2">
                {[
                  { id: 'dashboard', name: 'Tableau de bord', icon: Home },
                  { id: 'products', name: 'Produits', icon: Package },
                  { id: 'orders', name: 'Commandes', icon: ShoppingCart },
                  { id: 'clients', name: 'Clients', icon: Users },
                  { id: 'database', name: 'Base de données', icon: Database },
                  { id: 'reports', name: 'Rapports', icon: BarChart3 },
                  { id: 'settings', name: 'Paramètres', icon: Settings }
                ].map((item) => {
                  const Icon = item.icon;
                  return (
                    <button
                      key={item.id}
                      onClick={() => {
                        setActiveTab(item.id);
                        setShowMobileMenu(false);
                      }}
                      className={`w-full flex items-center space-x-3 px-4 py-3 rounded-2xl font-medium transition-all duration-300 ${
                        activeTab === item.id
                          ? 'bg-gradient-to-r from-orange-500 to-orange-600 text-white shadow-lg'
                          : 'text-gray-600 hover:bg-orange-50 hover:text-orange-600'
                      }`}
                    >
                      <Icon className="w-5 h-5" />
                      <span>{item.name}</span>
                    </button>
                  );
                })}

                <button
                  onClick={logout}
                  className="w-full flex items-center space-x-3 px-4 py-3 rounded-2xl font-medium text-red-600 hover:bg-red-50 transition-all duration-300 mt-4 border-t border-gray-200 pt-4"
                >
                  <LogOut className="w-5 h-5" />
                  <span>Déconnexion</span>
                </button>
              </nav>
            </div>
          </div>
        )}

        {/* Contenu principal */}
        <main className="flex-1 p-6">
          {activeTab === 'dashboard' && renderDashboard()}
          {activeTab === 'products' && renderProducts()}
          {activeTab === 'orders' && renderOrders()}
          {activeTab === 'clients' && renderClients()}
          {activeTab === 'database' && (
            <div className="space-y-6">
              <h2 className="text-2xl font-bold text-gray-900">Base de données</h2>
              <div className="bg-white rounded-3xl p-8 shadow-xl border border-orange-100 text-center">
                <Database className="w-24 h-24 text-orange-500 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-gray-900 mb-2">Gestion de la base de données</h3>
                <p className="text-gray-600 mb-6">Accédez aux outils avancés de gestion des données</p>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <button className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-bold py-4 px-6 rounded-2xl transition-all duration-300 transform hover:scale-105">
                    Sauvegarde
                  </button>
                  <button className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-bold py-4 px-6 rounded-2xl transition-all duration-300 transform hover:scale-105">
                    Restauration
                  </button>
                  <button className="bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white font-bold py-4 px-6 rounded-2xl transition-all duration-300 transform hover:scale-105">
                    Optimisation
                  </button>
                </div>
              </div>
            </div>
          )}
          {activeTab === 'reports' && (
            <div className="space-y-6">
              <h2 className="text-2xl font-bold text-gray-900">Rapports et analyses</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div className="bg-white rounded-3xl p-6 shadow-xl border border-orange-100">
                  <BarChart3 className="w-12 h-12 text-orange-500 mb-4" />
                  <h3 className="text-lg font-bold text-gray-900 mb-2">Rapport des ventes</h3>
                  <p className="text-gray-600 mb-4">Analyse détaillée des performances de vente</p>
                  <button className="w-full bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-bold py-3 px-4 rounded-2xl transition-all duration-300">
                    Générer
                  </button>
                </div>
                <div className="bg-white rounded-3xl p-6 shadow-xl border border-orange-100">
                  <PieChart className="w-12 h-12 text-blue-500 mb-4" />
                  <h3 className="text-lg font-bold text-gray-900 mb-2">Analyse des stocks</h3>
                  <p className="text-gray-600 mb-4">État des stocks et prévisions</p>
                  <button className="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-bold py-3 px-4 rounded-2xl transition-all duration-300">
                    Générer
                  </button>
                </div>
                <div className="bg-white rounded-3xl p-6 shadow-xl border border-orange-100">
                  <Users className="w-12 h-12 text-green-500 mb-4" />
                  <h3 className="text-lg font-bold text-gray-900 mb-2">Rapport clients</h3>
                  <p className="text-gray-600 mb-4">Analyse du comportement client</p>
                  <button className="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-bold py-3 px-4 rounded-2xl transition-all duration-300">
                    Générer
                  </button>
                </div>
              </div>
            </div>
          )}
          {activeTab === 'settings' && (
            <div className="space-y-6">
              <h2 className="text-2xl font-bold text-gray-900">Paramètres</h2>
              <div className="bg-white rounded-3xl p-8 shadow-xl border border-orange-100">
                <Settings className="w-24 h-24 text-purple-500 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-gray-900 mb-2 text-center">Configuration système</h3>
                <p className="text-gray-600 mb-6 text-center">Gérez les paramètres de l'application</p>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 bg-gray-50 rounded-2xl">
                    <span className="font-medium text-gray-900">Notifications email</span>
                    <button className="w-12 h-6 bg-orange-500 rounded-full relative">
                      <div className="w-5 h-5 bg-white rounded-full absolute right-0.5 top-0.5"></div>
                    </button>
                  </div>
                  <div className="flex items-center justify-between p-4 bg-gray-50 rounded-2xl">
                    <span className="font-medium text-gray-900">Mode maintenance</span>
                    <button className="w-12 h-6 bg-gray-300 rounded-full relative">
                      <div className="w-5 h-5 bg-white rounded-full absolute left-0.5 top-0.5"></div>
                    </button>
                  </div>
                  <div className="flex items-center justify-between p-4 bg-gray-50 rounded-2xl">
                    <span className="font-medium text-gray-900">Sauvegarde automatique</span>
                    <button className="w-12 h-6 bg-orange-500 rounded-full relative">
                      <div className="w-5 h-5 bg-white rounded-full absolute right-0.5 top-0.5"></div>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </main>
      </div>

      {/* Modal d'ajout/édition de produit */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-3xl p-8 max-w-md w-full shadow-2xl border border-orange-200 max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold text-gray-900">
                {editingProduct ? 'Modifier le produit' : 'Ajouter un produit'}
              </h2>
              <button
                onClick={() => setShowAddModal(false)}
                className="p-2 hover:bg-gray-100 rounded-full transition-colors duration-200"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            <form onSubmit={(e) => { e.preventDefault(); handleSaveProduct(); }} className="space-y-4">
              <div>
                <label className="block text-sm font-bold text-gray-800 mb-2">Nom du produit</label>
                <input
                  type="text"
                  value={formData.name || ''}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-orange-500 transition-all duration-300"
                  required
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-bold text-gray-800 mb-2">Prix (DH)</label>
                  <input
                    type="number"
                    value={formData.price || ''}
                    onChange={(e) => setFormData({ ...formData, price: Number(e.target.value) })}
                    className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-orange-500 transition-all duration-300"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-bold text-gray-800 mb-2">Stock</label>
                  <input
                    type="number"
                    value={formData.stock || ''}
                    onChange={(e) => setFormData({ ...formData, stock: Number(e.target.value) })}
                    className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-orange-500 transition-all duration-300"
                    required
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-bold text-gray-800 mb-2">Taille</label>
                  <input
                    type="text"
                    value={formData.size || ''}
                    onChange={(e) => setFormData({ ...formData, size: e.target.value })}
                    className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-orange-500 transition-all duration-300"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-bold text-gray-800 mb-2">Couleur</label>
                  <input
                    type="text"
                    value={formData.color || ''}
                    onChange={(e) => setFormData({ ...formData, color: e.target.value })}
                    className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-orange-500 transition-all duration-300"
                    required
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-bold text-gray-800 mb-2">Catégorie</label>
                <select
                  value={formData.category || 'doors'}
                  onChange={(e) => setFormData({ ...formData, category: e.target.value as Product['category'] })}
                  className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-orange-500 transition-all duration-300"
                  required
                >
                  <option value="doors">Portes</option>
                  <option value="windows">Fenêtres</option>
                  <option value="aluminum">Aluminium</option>
                  <option value="carpentry">Menuiserie</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-bold text-gray-800 mb-2">Description</label>
                <textarea
                  value={formData.description || ''}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-orange-500 transition-all duration-300"
                  rows={3}
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-bold text-gray-800 mb-2">URL de l'image</label>
                <input
                  type="url"
                  value={formData.image || ''}
                  onChange={(e) => setFormData({ ...formData, image: e.target.value })}
                  className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-orange-500 transition-all duration-300"
                  required
                />
              </div>

              <div className="flex space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setShowAddModal(false)}
                  className="flex-1 bg-gray-500 hover:bg-gray-600 text-white font-bold py-4 px-6 rounded-2xl transition-all duration-300"
                >
                  Annuler
                </button>
                <button
                  type="submit"
                  className="flex-1 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-bold py-4 px-6 rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-lg flex items-center justify-center space-x-2"
                >
                  <Save className="w-5 h-5" />
                  <span>{editingProduct ? 'Modifier' : 'Ajouter'}</span>
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default ModernAdminDashboard;