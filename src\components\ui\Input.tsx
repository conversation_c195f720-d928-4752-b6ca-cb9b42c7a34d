import React, { forwardRef } from 'react';
import { clsx } from 'clsx';

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  helperText?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  variant?: 'default' | 'filled' | 'glass';
  inputSize?: 'sm' | 'md' | 'lg';
}

const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      className,
      label,
      error,
      helperText,
      leftIcon,
      rightIcon,
      variant = 'default',
      inputSize = 'md',
      id,
      ...props
    },
    ref
  ) => {
    const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;

    const baseClasses = [
      'w-full transition-all duration-300',
      'focus:outline-none focus:ring-2 focus:ring-offset-2',
      'disabled:opacity-50 disabled:cursor-not-allowed',
    ];

    const variantClasses = {
      default: [
        'bg-white border border-gray-200',
        'hover:border-gray-300',
        'focus:border-orange-500 focus:ring-orange-500',
        'shadow-sm hover:shadow-md focus:shadow-lg',
      ],
      filled: [
        'bg-gray-50 border border-transparent',
        'hover:bg-gray-100',
        'focus:bg-white focus:border-orange-500 focus:ring-orange-500',
        'shadow-sm hover:shadow-md focus:shadow-lg',
      ],
      glass: [
        'glass border border-white/20',
        'hover:border-white/30',
        'focus:border-orange-500 focus:ring-orange-500',
        'placeholder-gray-400',
      ],
    };

    const sizeClasses = {
      sm: 'px-3 py-2 text-sm rounded-lg',
      md: 'px-4 py-3 text-base rounded-xl',
      lg: 'px-5 py-4 text-lg rounded-2xl',
    };

    const inputClasses = clsx(
      baseClasses,
      variantClasses[variant],
      sizeClasses[inputSize],
      {
        'pl-10': leftIcon && inputSize === 'sm',
        'pl-11': leftIcon && inputSize === 'md',
        'pl-12': leftIcon && inputSize === 'lg',
        'pr-10': rightIcon && inputSize === 'sm',
        'pr-11': rightIcon && inputSize === 'md',
        'pr-12': rightIcon && inputSize === 'lg',
        'border-red-300 focus:border-red-500 focus:ring-red-500': error,
      },
      className
    );

    const iconSizeClasses = {
      sm: 'w-4 h-4',
      md: 'w-5 h-5',
      lg: 'w-6 h-6',
    };

    const iconPositionClasses = {
      left: {
        sm: 'left-3',
        md: 'left-3',
        lg: 'left-4',
      },
      right: {
        sm: 'right-3',
        md: 'right-3',
        lg: 'right-4',
      },
    };

    return (
      <div className="w-full">
        {label && (
          <label
            htmlFor={inputId}
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            {label}
          </label>
        )}
        <div className="relative">
          {leftIcon && (
            <div
              className={clsx(
                'absolute inset-y-0 flex items-center pointer-events-none text-gray-400',
                iconPositionClasses.left[inputSize]
              )}
            >
              <span className={iconSizeClasses[inputSize]}>{leftIcon}</span>
            </div>
          )}
          <input
            ref={ref}
            id={inputId}
            className={inputClasses}
            {...props}
          />
          {rightIcon && (
            <div
              className={clsx(
                'absolute inset-y-0 flex items-center pointer-events-none text-gray-400',
                iconPositionClasses.right[inputSize]
              )}
            >
              <span className={iconSizeClasses[inputSize]}>{rightIcon}</span>
            </div>
          )}
        </div>
        {error && (
          <p className="mt-2 text-sm text-red-600 animate-fade-in-up">
            {error}
          </p>
        )}
        {helperText && !error && (
          <p className="mt-2 text-sm text-gray-500">
            {helperText}
          </p>
        )}
      </div>
    );
  }
);

Input.displayName = 'Input';

export default Input;
