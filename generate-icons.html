<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Générateur d'icônes Doorly</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .icon-preview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        .icon-item {
            text-align: center;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 10px;
        }
        .icon-svg {
            margin-bottom: 10px;
        }
        button {
            background: linear-gradient(135deg, #f97316, #ea580c);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            margin: 10px 5px;
        }
        button:hover {
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Générateur d'icônes Doorly</h1>
        <p>Cliquez sur "Générer toutes les icônes" pour créer automatiquement toutes les tailles d'icônes nécessaires pour votre PWA.</p>
        
        <button onclick="generateAllIcons()">🚀 Générer toutes les icônes</button>
        <button onclick="downloadZip()">📦 Télécharger le ZIP</button>
        
        <div class="icon-preview" id="iconPreview">
            <!-- Les icônes générées apparaîtront ici -->
        </div>
    </div>

    <script>
        const iconSizes = [72, 96, 128, 144, 152, 180, 192, 384, 512];
        const generatedIcons = {};

        function createIconSVG(size) {
            const svg = `
                <svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
                    <defs>
                        <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#f97316;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#ea580c;stop-opacity:1" />
                        </linearGradient>
                        <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
                            <feDropShadow dx="0" dy="${size * 0.02}" stdDeviation="${size * 0.01}" flood-color="rgba(0,0,0,0.3)"/>
                        </filter>
                    </defs>
                    
                    <!-- Fond avec coins arrondis -->
                    <rect width="${size}" height="${size}" rx="${size * 0.2}" ry="${size * 0.2}" fill="url(#grad)" filter="url(#shadow)"/>
                    
                    <!-- Icône de porte stylisée -->
                    <g transform="translate(${size * 0.2}, ${size * 0.15})">
                        <!-- Cadre de porte -->
                        <rect x="0" y="0" width="${size * 0.6}" height="${size * 0.7}" rx="${size * 0.05}" fill="white" opacity="0.9"/>
                        
                        <!-- Porte -->
                        <rect x="${size * 0.05}" y="${size * 0.05}" width="${size * 0.5}" height="${size * 0.6}" rx="${size * 0.03}" fill="white"/>
                        
                        <!-- Poignée -->
                        <circle cx="${size * 0.45}" cy="${size * 0.35}" r="${size * 0.02}" fill="#f97316"/>
                        
                        <!-- Détails décoratifs -->
                        <rect x="${size * 0.1}" y="${size * 0.1}" width="${size * 0.4}" height="${size * 0.15}" rx="${size * 0.02}" fill="none" stroke="#f97316" stroke-width="${size * 0.005}" opacity="0.7"/>
                        <rect x="${size * 0.1}" y="${size * 0.45}" width="${size * 0.4}" height="${size * 0.15}" rx="${size * 0.02}" fill="none" stroke="#f97316" stroke-width="${size * 0.005}" opacity="0.7"/>
                    </g>
                    
                    <!-- Texte Doorly (pour les grandes tailles) -->
                    ${size >= 192 ? `
                        <text x="${size/2}" y="${size * 0.95}" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-weight="bold" font-size="${size * 0.08}" opacity="0.9">DOORLY</text>
                    ` : ''}
                </svg>
            `;
            return svg;
        }

        function svgToCanvas(svgString, size) {
            return new Promise((resolve) => {
                const canvas = document.createElement('canvas');
                canvas.width = size;
                canvas.height = size;
                const ctx = canvas.getContext('2d');
                
                const img = new Image();
                const svgBlob = new Blob([svgString], { type: 'image/svg+xml' });
                const url = URL.createObjectURL(svgBlob);
                
                img.onload = () => {
                    ctx.drawImage(img, 0, 0, size, size);
                    URL.revokeObjectURL(url);
                    resolve(canvas);
                };
                
                img.src = url;
            });
        }

        async function generateIcon(size) {
            const svgString = createIconSVG(size);
            const canvas = await svgToCanvas(svgString, size);
            
            return new Promise((resolve) => {
                canvas.toBlob((blob) => {
                    generatedIcons[`icon-${size}x${size}.png`] = blob;
                    resolve({
                        size,
                        dataUrl: canvas.toDataURL(),
                        blob
                    });
                }, 'image/png');
            });
        }

        async function generateAllIcons() {
            const preview = document.getElementById('iconPreview');
            preview.innerHTML = '<p>🔄 Génération des icônes en cours...</p>';
            
            const icons = [];
            
            for (const size of iconSizes) {
                try {
                    const icon = await generateIcon(size);
                    icons.push(icon);
                    
                    // Mettre à jour l'aperçu
                    const iconDiv = document.createElement('div');
                    iconDiv.className = 'icon-item';
                    iconDiv.innerHTML = `
                        <div class="icon-svg">
                            <img src="${icon.dataUrl}" alt="Icon ${size}x${size}" style="width: 64px; height: 64px; border-radius: 12px; box-shadow: 0 2px 8px rgba(0,0,0,0.2);">
                        </div>
                        <strong>${size}x${size}</strong>
                        <br>
                        <small>icon-${size}x${size}.png</small>
                        <br>
                        <button onclick="downloadIcon('icon-${size}x${size}.png')">⬇️ Télécharger</button>
                    `;
                    
                    if (preview.children.length === 1 && preview.children[0].tagName === 'P') {
                        preview.innerHTML = '';
                    }
                    preview.appendChild(iconDiv);
                    
                } catch (error) {
                    console.error(`Erreur lors de la génération de l'icône ${size}x${size}:`, error);
                }
            }
            
            console.log('✅ Toutes les icônes ont été générées!');
            
            // Ajouter un message de succès
            const successDiv = document.createElement('div');
            successDiv.style.cssText = 'grid-column: 1 / -1; text-align: center; padding: 20px; background: #d1fae5; border-radius: 10px; color: #065f46; font-weight: 600; margin-top: 20px;';
            successDiv.innerHTML = '🎉 Toutes les icônes ont été générées avec succès!<br><small>Vous pouvez maintenant les télécharger individuellement ou toutes en une fois.</small>';
            preview.appendChild(successDiv);
        }

        function downloadIcon(filename) {
            const blob = generatedIcons[filename];
            if (blob) {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            }
        }

        async function downloadZip() {
            if (Object.keys(generatedIcons).length === 0) {
                alert('⚠️ Veuillez d\'abord générer les icônes!');
                return;
            }

            // Créer un fichier ZIP simple (simulation)
            const zipContent = [];
            
            for (const [filename, blob] of Object.entries(generatedIcons)) {
                const arrayBuffer = await blob.arrayBuffer();
                zipContent.push({
                    filename,
                    content: arrayBuffer
                });
            }
            
            // Pour une vraie implémentation, vous devriez utiliser une bibliothèque comme JSZip
            alert('📦 Fonctionnalité ZIP en cours de développement. Téléchargez les icônes individuellement pour le moment.');
            
            console.log('Contenu du ZIP:', zipContent);
        }

        // Instructions d'utilisation
        console.log(`
🎨 Générateur d'icônes Doorly
=============================

Instructions:
1. Cliquez sur "Générer toutes les icônes"
2. Attendez que toutes les icônes soient créées
3. Téléchargez les icônes individuellement
4. Placez-les dans le dossier public/ de votre projet

Tailles générées: ${iconSizes.join(', ')}

Les icônes sont optimisées pour:
- PWA (Progressive Web App)
- iOS (Apple Touch Icons)
- Android (Adaptive Icons)
- Favicon haute résolution
        `);
    </script>
</body>
</html>
