import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { 
  DoorOpen as Door, 
  User, 
  Phone, 
  Lock, 
  AlertCircle, 
  QrCode,
  Smartphone,
  Eye,
  EyeOff,
  ArrowRight,
  Sparkles,
  Shield,
  Zap,
  Star,
  Wifi,
  Globe,
  CheckCircle
} from 'lucide-react';

const ModernLoginForm: React.FC = () => {
  const [role, setRole] = useState<'admin' | 'client'>('client');
  const [identifier, setIdentifier] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showQR, setShowQR] = useState(false);
  const [animationStep, setAnimationStep] = useState(0);
  const { login } = useAuth();

  // Animation d'entrée
  useEffect(() => {
    const timer = setTimeout(() => setAnimationStep(1), 100);
    return () => clearTimeout(timer);
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      const success = await login(identifier, password, role);
      if (!success) {
        setError('Identifiants incorrects');
      }
    } catch (err) {
      setError('Une erreur est survenue');
    } finally {
      setLoading(false);
    }
  };

  // Composant QR Code ultra-moderne
  const QRCodeComponent = () => (
    <div className="bg-white/10 backdrop-blur-2xl p-8 rounded-3xl shadow-2xl border border-white/20 animate-fade-in-scale">
      <div className="text-center mb-6">
        <div className="w-16 h-16 bg-gradient-to-r from-orange-500 to-pink-500 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-2xl shadow-orange-500/25">
          <QrCode className="w-8 h-8 text-white" />
        </div>
        <h3 className="text-xl font-bold text-white mb-2">Connexion Express</h3>
        <p className="text-gray-300">Scannez avec l'app mobile Doorly</p>
      </div>
      
      {/* QR Code ultra-moderne avec animation */}
      <div className="w-56 h-56 mx-auto bg-white rounded-3xl p-6 relative overflow-hidden shadow-2xl">
        <div className="absolute inset-0 bg-gradient-to-br from-orange-500/10 to-pink-500/10 rounded-3xl"></div>
        
        {/* Pattern QR animé */}
        <div className="relative z-10 grid grid-cols-12 gap-1 h-full">
          {Array.from({ length: 144 }, (_, i) => (
            <div
              key={i}
              className={`rounded-sm transition-all duration-500 ${
                Math.random() > 0.6 ? 'bg-gray-900' : 'bg-transparent'
              }`}
              style={{
                animationDelay: `${Math.random() * 2}s`,
              }}
            />
          ))}
        </div>
        
        {/* Logo central animé */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="bg-white rounded-2xl p-3 shadow-xl border-2 border-orange-200 animate-pulse-slow">
            <Door className="w-8 h-8 text-orange-500" />
          </div>
        </div>
        
        {/* Effet de scan */}
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-orange-500/20 to-transparent h-8 animate-bounce-gentle"></div>
      </div>
      
      <div className="mt-6 space-y-3">
        <div className="flex items-center justify-center space-x-2 text-green-400">
          <CheckCircle className="w-4 h-4" />
          <span className="text-sm font-medium">Connexion sécurisée</span>
        </div>
        <p className="text-xs text-gray-400 text-center">
          Code valide pendant 5 minutes
        </p>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
      {/* Background animé ultra-moderne */}
      <div className="absolute inset-0">
        {/* Orbes lumineux */}
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-orange-500/10 rounded-full blur-3xl animate-pulse-slow"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse-slow" style={{ animationDelay: '1s' }}></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl animate-pulse-slow" style={{ animationDelay: '2s' }}></div>
        
        {/* Grille de fond */}
        <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:50px_50px]"></div>
      </div>

      {/* Particules flottantes */}
      <div className="absolute inset-0 overflow-hidden">
        {Array.from({ length: 30 }, (_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-white/30 rounded-full animate-bounce-gentle"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 5}s`,
              animationDuration: `${4 + Math.random() * 3}s`,
            }}
          />
        ))}
      </div>

      <div className="relative z-10 min-h-screen flex items-center justify-center p-4">
        <div className={`w-full max-w-7xl grid lg:grid-cols-2 gap-12 items-center transition-all duration-1000 ${animationStep ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
          
          {/* Section gauche - Branding & QR */}
          <div className="text-center lg:text-left space-y-8">
            <div className="space-y-8">
              {/* Badge nouvelle expérience */}
              <div className="inline-flex items-center space-x-3 bg-white/10 backdrop-blur-xl rounded-full px-6 py-3 border border-white/20 shadow-2xl">
                <Sparkles className="w-5 h-5 text-orange-400 animate-pulse" />
                <span className="text-white font-medium">Expérience Ultra-Moderne</span>
                <Star className="w-4 h-4 text-yellow-400" />
              </div>
              
              {/* Titre principal */}
              <div>
                <h1 className="text-7xl lg:text-8xl font-black text-white mb-6 leading-tight">
                  <span className="bg-gradient-to-r from-orange-400 via-pink-400 to-purple-400 bg-clip-text text-transparent animate-pulse">
                    Doorly
                  </span>
                </h1>
                <p className="text-2xl text-gray-300 mb-8 leading-relaxed">
                  L'avenir des portes et fenêtres premium.<br />
                  <span className="text-transparent bg-gradient-to-r from-orange-400 to-pink-400 bg-clip-text font-bold">
                    Design • Innovation • Excellence
                  </span>
                </p>
              </div>

              {/* Features badges */}
              <div className="flex flex-wrap gap-4 justify-center lg:justify-start">
                <div className="flex items-center space-x-2 bg-white/5 backdrop-blur-xl rounded-2xl px-4 py-3 border border-white/10 shadow-xl">
                  <Shield className="w-5 h-5 text-green-400" />
                  <span className="text-white text-sm font-medium">100% Sécurisé</span>
                </div>
                <div className="flex items-center space-x-2 bg-white/5 backdrop-blur-xl rounded-2xl px-4 py-3 border border-white/10 shadow-xl">
                  <Zap className="w-5 h-5 text-yellow-400" />
                  <span className="text-white text-sm font-medium">Ultra-Rapide</span>
                </div>
                <div className="flex items-center space-x-2 bg-white/5 backdrop-blur-xl rounded-2xl px-4 py-3 border border-white/10 shadow-xl">
                  <Smartphone className="w-5 h-5 text-blue-400" />
                  <span className="text-white text-sm font-medium">Mobile-First</span>
                </div>
                <div className="flex items-center space-x-2 bg-white/5 backdrop-blur-xl rounded-2xl px-4 py-3 border border-white/10 shadow-xl">
                  <Globe className="w-5 h-5 text-purple-400" />
                  <span className="text-white text-sm font-medium">Connecté</span>
                </div>
              </div>
            </div>

            {/* Bouton QR Code */}
            <div className="flex justify-center lg:justify-start">
              <button
                onClick={() => setShowQR(!showQR)}
                className="group flex items-center space-x-3 bg-gradient-to-r from-orange-500 via-pink-500 to-purple-500 hover:from-orange-600 hover:via-pink-600 hover:to-purple-600 text-white px-8 py-4 rounded-2xl font-bold transition-all duration-300 transform hover:scale-105 shadow-2xl shadow-orange-500/25 hover:shadow-orange-500/40"
              >
                <QrCode className="w-6 h-6 group-hover:rotate-12 transition-transform duration-300" />
                <span>{showQR ? 'Masquer QR Code' : 'Connexion QR Express'}</span>
                <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
              </button>
            </div>

            {/* QR Code Section */}
            {showQR && (
              <div className="flex justify-center lg:justify-start">
                <QRCodeComponent />
              </div>
            )}
          </div>

          {/* Section droite - Formulaire de connexion */}
          <div className="w-full max-w-md mx-auto lg:mx-0">
            <div className="bg-white/10 backdrop-blur-2xl rounded-3xl p-8 border border-white/20 shadow-2xl">
              <div className="text-center mb-8">
                <div className="w-20 h-20 bg-gradient-to-r from-orange-500 via-pink-500 to-purple-500 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-2xl shadow-orange-500/25 animate-pulse-slow">
                  <Door className="w-10 h-10 text-white" />
                </div>
                <h2 className="text-3xl font-bold text-white mb-2">Connexion</h2>
                <p className="text-gray-300">Accédez à votre espace premium</p>
              </div>

              {/* Sélection du rôle */}
              <div className="flex mb-8 bg-white/5 backdrop-blur-xl rounded-2xl p-1 border border-white/10">
                <button
                  type="button"
                  onClick={() => setRole('client')}
                  className={`flex-1 py-4 px-4 rounded-xl font-bold transition-all duration-300 ${
                    role === 'client'
                      ? 'bg-gradient-to-r from-orange-500 to-pink-500 text-white shadow-xl transform scale-105'
                      : 'text-gray-300 hover:text-white hover:bg-white/10'
                  }`}
                >
                  <User className="w-5 h-5 inline mr-2" />
                  Client Premium
                </button>
                <button
                  type="button"
                  onClick={() => setRole('admin')}
                  className={`flex-1 py-4 px-4 rounded-xl font-bold transition-all duration-300 ${
                    role === 'admin'
                      ? 'bg-gradient-to-r from-purple-500 to-indigo-500 text-white shadow-xl transform scale-105'
                      : 'text-gray-300 hover:text-white hover:bg-white/10'
                  }`}
                >
                  <Lock className="w-5 h-5 inline mr-2" />
                  Administrateur
                </button>
              </div>

              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Champ identifiant */}
                <div>
                  <label className="block text-sm font-bold text-white mb-3">
                    {role === 'admin' ? 'Nom d\'utilisateur' : 'Numéro de téléphone'}
                  </label>
                  <div className="relative">
                    {role === 'admin' ? (
                      <User className="absolute left-4 top-1/2 transform -translate-y-1/2 w-6 h-6 text-orange-400" />
                    ) : (
                      <Phone className="absolute left-4 top-1/2 transform -translate-y-1/2 w-6 h-6 text-orange-400" />
                    )}
                    <input
                      type={role === 'admin' ? 'text' : 'tel'}
                      value={identifier}
                      onChange={(e) => setIdentifier(e.target.value)}
                      className="w-full pl-14 pr-4 py-4 bg-white/10 backdrop-blur-xl border border-white/20 rounded-2xl focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-300 text-white placeholder-gray-300 font-medium"
                      placeholder={role === 'admin' ? 'admin' : '+212 6XX XXX XXX'}
                      required
                    />
                  </div>
                </div>

                {/* Champ mot de passe */}
                <div>
                  <label className="block text-sm font-bold text-white mb-3">
                    Mot de passe
                  </label>
                  <div className="relative">
                    <Lock className="absolute left-4 top-1/2 transform -translate-y-1/2 w-6 h-6 text-orange-400" />
                    <input
                      type={showPassword ? 'text' : 'password'}
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      className="w-full pl-14 pr-14 py-4 bg-white/10 backdrop-blur-xl border border-white/20 rounded-2xl focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-300 text-white placeholder-gray-300 font-medium"
                      placeholder={role === 'admin' ? 'admin123' : 'Votre mot de passe'}
                      required
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors duration-200"
                    >
                      {showPassword ? <EyeOff className="w-6 h-6" /> : <Eye className="w-6 h-6" />}
                    </button>
                  </div>
                </div>

                {/* Message d'erreur */}
                {error && (
                  <div className="flex items-center space-x-2 bg-red-500/20 backdrop-blur-xl border border-red-500/30 rounded-2xl p-4 animate-fade-in-up">
                    <AlertCircle className="w-5 h-5 text-red-400" />
                    <span className="text-red-300 font-medium">{error}</span>
                  </div>
                )}

                {/* Bouton de connexion */}
                <button
                  type="submit"
                  disabled={loading}
                  className="w-full bg-gradient-to-r from-orange-500 via-pink-500 to-purple-500 hover:from-orange-600 hover:via-pink-600 hover:to-purple-600 text-white font-bold py-4 px-6 rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-2xl shadow-orange-500/25 hover:shadow-orange-500/40 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center space-x-3"
                >
                  {loading ? (
                    <>
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white"></div>
                      <span>Connexion en cours...</span>
                    </>
                  ) : (
                    <>
                      <span>Se connecter</span>
                      <ArrowRight className="w-5 h-5" />
                    </>
                  )}
                </button>
              </form>

              {/* Informations de connexion */}
              <div className="mt-8 p-4 bg-white/5 backdrop-blur-xl rounded-2xl border border-white/10">
                <h4 className="text-white font-bold mb-2">Comptes de démonstration :</h4>
                <div className="space-y-1 text-sm text-gray-300">
                  <p><strong>Admin:</strong> admin / admin123</p>
                  <p><strong>Client:</strong> 12345678 / motdepasse</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ModernLoginForm;
