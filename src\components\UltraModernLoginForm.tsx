import React, { useState } from 'react';
import { ArrowLeft, Phone, Lock, Eye, EyeOff, User, Shield, Crown } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';

interface UltraModernLoginFormProps {
  onBack: () => void;
}

const UltraModernLoginForm: React.FC<UltraModernLoginFormProps> = ({ onBack }) => {
  const [identifier, setIdentifier] = useState(''); // Phone or username
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [focusedField, setFocusedField] = useState<string | null>(null);
  const { login } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    // Accept any credentials and always redirect to client dashboard
    setTimeout(() => {
      if (identifier && password) {
        login({
          id: Date.now().toString(),
          email: '<EMAIL>',
          name: 'Client Doorly',
          role: 'client'
        });
      } else {
        alert('Veuillez remplir tous les champs');
      }
      setIsLoading(false);
    }, 800);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-400 via-orange-500 to-orange-600 relative overflow-hidden perspective-5d">
      {/* Background Pattern 5D */}
      <div className="absolute inset-0 opacity-15">
        <div className="absolute top-20 left-10 w-32 h-32 bg-white rounded-full blur-xl animate-float-5d-ultra"></div>
        <div className="absolute top-40 right-20 w-24 h-24 bg-white rounded-full blur-lg animate-depth-field-blur"></div>
        <div className="absolute bottom-32 left-1/4 w-40 h-40 bg-white rounded-full blur-2xl animate-quantum-glow"></div>
        <div className="absolute bottom-20 right-10 w-28 h-28 bg-white rounded-full blur-xl animate-holographic-shift"></div>
      </div>

      {/* Floating Geometric Shapes 5D */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/4 left-8 w-8 h-8 bg-white/20 rounded-lg rotate-45 animate-float-5d-ultra transform-5d"></div>
        <div className="absolute top-1/3 right-12 w-6 h-6 bg-white/30 rounded-full animate-depth-field-blur transform-5d"></div>
        <div className="absolute bottom-1/3 left-16 w-10 h-10 bg-white/25 rounded-lg rotate-12 animate-quantum-glow transform-5d"></div>
        <div className="absolute top-2/3 right-1/4 w-4 h-4 bg-white/40 rounded-full animate-holographic-shift transform-5d"></div>
        <div className="absolute top-1/2 left-1/2 w-12 h-12 bg-white/15 rounded-full animate-glassmorphism-5d transform-5d"></div>
      </div>

      <div className="flex flex-col min-h-screen px-6 py-8 relative z-10">
        {/* Header */}
        <div className="flex items-center mb-8 animate-fade-in-up">
          <button
            onClick={onBack}
            className="p-3 bg-white/20 rounded-full backdrop-blur-sm mr-4 hover:scale-110 hover:bg-white/30 transition-all duration-300 group"
          >
            <ArrowLeft className="w-6 h-6 text-white group-hover:text-orange-100" />
          </button>
          <h1 className="text-2xl font-bold text-white">Se Connecter</h1>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex items-center justify-center perspective-5d">
          <div className="w-full max-w-md animate-fade-in-up-delay transform-5d">
            {/* Login Card 5D */}
            <div className="glass-5d-orange rounded-3xl p-8 shadow-2xl border border-orange-200 relative overflow-hidden animate-glassmorphism-5d transform-5d">
              {/* Card Background Effect 5D */}
              <div className="absolute inset-0 bg-gradient-to-br from-orange-50/30 to-white/20 rounded-3xl animate-holographic-shift"></div>

              {/* Quantum Glow Effect */}
              <div className="absolute inset-0 rounded-3xl animate-quantum-glow opacity-50"></div>

              {/* Logo Doorly 5D */}
              <div className="text-center mb-6 relative z-20">
                <div className="relative w-16 h-20 mx-auto mb-4 animate-float-5d-ultra transform-5d">
                  {/* Door Shadow 5D */}
                  <div className="absolute top-1 left-1 w-full h-full bg-black/30 rounded-lg transform rotate-2 blur-md animate-depth-field-blur"></div>

                  {/* Main Door 5D */}
                  <div className="relative w-full h-full bg-gradient-to-br from-amber-100 to-amber-200 rounded-lg shadow-2xl transform -rotate-1 border-2 border-amber-300 animate-quantum-glow">
                    {/* Door Frame */}
                    <div className="absolute inset-1 border border-amber-400 rounded-md animate-holographic-shift"></div>

                    {/* Door Handle */}
                    <div className="absolute right-1 top-1/2 transform -translate-y-1/2">
                      <div className="w-1.5 h-1.5 bg-gradient-to-br from-yellow-600 to-yellow-800 rounded-full shadow-lg animate-quantum-glow"></div>
                    </div>

                    {/* 5D Holographic Effect */}
                    <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-lg animate-holographic-shift"></div>
                  </div>
                </div>

                <h1 className="text-2xl font-black text-orange-600 mb-1 animate-quantum-glow">DOORLY</h1>
                <h2 className="text-xl font-bold text-gray-800 mb-2">Bienvenue !</h2>
                <p className="text-gray-600">Connectez-vous à votre compte</p>
              </div>



              <form onSubmit={handleSubmit} className="space-y-6 relative z-20">
                {/* Identifier Field 5D */}
                <div className="relative transform-5d">
                  <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10">
                    <User className={`h-5 w-5 transition-all duration-500 ${
                      focusedField === 'identifier' ? 'text-orange-500 animate-quantum-glow' : 'text-gray-400'
                    }`} />
                  </div>
                  <input
                    type="text"
                    value={identifier}
                    onChange={(e) => setIdentifier(e.target.value)}
                    onFocus={() => setFocusedField('identifier')}
                    onBlur={() => setFocusedField(null)}
                    placeholder="Nom d'utilisateur ou téléphone"
                    className="w-full pl-12 pr-4 py-4 glass-5d-orange border-2 border-orange-200 rounded-2xl text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 focus:scale-105 shadow-2xl transition-all duration-500 animate-glassmorphism-5d"
                    required
                  />
                  {focusedField === 'identifier' && (
                    <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-orange-400/20 to-orange-600/20 -z-10 blur-lg animate-quantum-glow"></div>
                  )}
                  <div className="absolute inset-0 rounded-2xl animate-holographic-shift -z-20"></div>
                </div>

                {/* Password Field 5D */}
                <div className="relative transform-5d">
                  <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10">
                    <Lock className={`h-5 w-5 transition-all duration-500 ${
                      focusedField === 'password' ? 'text-orange-500 animate-quantum-glow' : 'text-gray-400'
                    }`} />
                  </div>
                  <input
                    type={showPassword ? 'text' : 'password'}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    onFocus={() => setFocusedField('password')}
                    onBlur={() => setFocusedField(null)}
                    placeholder="Votre mot de passe"
                    className="w-full pl-12 pr-12 py-4 glass-5d-orange border-2 border-orange-200 rounded-2xl text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 focus:scale-105 shadow-2xl transition-all duration-500 animate-glassmorphism-5d"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-orange-500 transition-all duration-500 z-10 hover:animate-quantum-glow"
                  >
                    {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                  </button>
                  {focusedField === 'password' && (
                    <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-orange-400/20 to-orange-600/20 -z-10 blur-lg animate-quantum-glow"></div>
                  )}
                  <div className="absolute inset-0 rounded-2xl animate-holographic-shift -z-20"></div>
                </div>

                {/* Login Button 5D */}
                <button
                  type="submit"
                  disabled={isLoading}
                  className="w-full py-4 px-8 bg-gradient-to-r from-orange-500 to-orange-600 text-white font-bold text-lg rounded-2xl shadow-2xl hover:shadow-3xl hover:scale-110 hover:-translate-y-2 transition-all duration-500 relative overflow-hidden group disabled:opacity-50 disabled:cursor-not-allowed transform-5d animate-quantum-glow"
                >
                  <span className="relative z-20 flex items-center justify-center">
                    {isLoading ? (
                      <>
                        <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2 animate-quantum-glow"></div>
                        Connexion...
                      </>
                    ) : (
                      'Se Connecter'
                    )}
                  </span>
                  <div className="absolute inset-0 bg-gradient-to-r from-orange-600 to-orange-700 opacity-0 group-hover:opacity-100 transition-all duration-500 animate-holographic-shift"></div>
                  <div className="absolute inset-0 animate-glassmorphism-5d opacity-30"></div>
                </button>


              </form>
            </div>

            {/* Security Badge */}
            <div className="flex items-center justify-center mt-6 text-gray-500">
              <Shield className="w-4 h-4 mr-2" />
              <span className="text-sm">Connexion sécurisée SSL</span>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center text-white/80 text-sm animate-fade-in-up-delay-4">
          <p>© 2024 Doorly - Votre solution moderne pour portes et fenêtres</p>
        </div>
      </div>
    </div>
  );
};

export default UltraModernLoginForm;
