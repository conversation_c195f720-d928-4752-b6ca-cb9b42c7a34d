import React, { useState } from 'react';
import { <PERSON><PERSON>eft, Lock, Eye, EyeOff, User } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';

interface UltraModernLoginFormProps {
  onBack: () => void;
}

const UltraModernLoginForm: React.FC<UltraModernLoginFormProps> = ({ onBack }) => {
  const [userType, setUserType] = useState<'client' | 'admin'>('client');
  const [identifier, setIdentifier] = useState(''); // Phone for client, name for admin
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [focusedField, setFocusedField] = useState<string | null>(null);
  const { login } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    // Accept any credentials and always redirect to client dashboard (products page)
    setTimeout(() => {
      if (identifier && password) {
        login({
          id: Date.now().toString(),
          email: userType === 'admin' ? '<EMAIL>' : '<EMAIL>',
          name: userType === 'admin' ? 'Admin Doorly' : 'Client Doorly',
          role: 'client' // Always redirect to client dashboard for products
        });
      } else {
        alert('Veuillez remplir tous les champs');
      }
      setIsLoading(false);
    }, 800);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-400 via-orange-500 to-orange-600 relative overflow-hidden perspective-5d">
      {/* Background Pattern 5D */}
      <div className="absolute inset-0 opacity-15">
        <div className="absolute top-20 left-10 w-32 h-32 bg-white rounded-full blur-xl animate-float-5d-ultra"></div>
        <div className="absolute top-40 right-20 w-24 h-24 bg-white rounded-full blur-lg animate-depth-field-blur"></div>
        <div className="absolute bottom-32 left-1/4 w-40 h-40 bg-white rounded-full blur-2xl animate-quantum-glow"></div>
        <div className="absolute bottom-20 right-10 w-28 h-28 bg-white rounded-full blur-xl animate-holographic-shift"></div>
      </div>

      {/* Floating Geometric Shapes 5D */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/4 left-8 w-8 h-8 bg-white/20 rounded-lg rotate-45 animate-float-5d-ultra transform-5d"></div>
        <div className="absolute top-1/3 right-12 w-6 h-6 bg-white/30 rounded-full animate-depth-field-blur transform-5d"></div>
        <div className="absolute bottom-1/3 left-16 w-10 h-10 bg-white/25 rounded-lg rotate-12 animate-quantum-glow transform-5d"></div>
        <div className="absolute top-2/3 right-1/4 w-4 h-4 bg-white/40 rounded-full animate-holographic-shift transform-5d"></div>
        <div className="absolute top-1/2 left-1/2 w-12 h-12 bg-white/15 rounded-full animate-glassmorphism-5d transform-5d"></div>
      </div>

      <div className="flex flex-col min-h-screen px-6 py-8 relative z-10">
        {/* Header */}
        <div className="flex items-center mb-8 animate-fade-in-up">
          <button
            onClick={onBack}
            className="p-3 bg-white/20 rounded-full backdrop-blur-sm mr-4 hover:scale-110 hover:bg-white/30 transition-all duration-300 group"
          >
            <ArrowLeft className="w-6 h-6 text-white group-hover:text-orange-100" />
          </button>
          <h1 className="text-2xl font-bold text-white">Se Connecter</h1>
        </div>

        {/* Main Content - Mobile Card Design */}
        <div className="flex-1 flex items-center justify-center perspective-5d px-4">
          <div className="w-full max-w-sm animate-fade-in-up-delay transform-5d">
            {/* Mobile Login Card - Orange Design */}
            <div className="bg-gradient-to-br from-orange-400 to-orange-500 rounded-3xl p-6 shadow-2xl relative overflow-hidden animate-glassmorphism-5d transform-5d border-2 border-orange-300">
              {/* Card Background Effect 5D */}
              <div className="absolute inset-0 bg-gradient-to-br from-orange-300/30 to-orange-600/20 rounded-3xl animate-holographic-shift"></div>

              {/* Quantum Glow Effect */}
              <div className="absolute inset-0 rounded-3xl animate-quantum-glow opacity-20"></div>

              {/* Header - Mobile Style */}
              <div className="text-center mb-6 relative z-20">
                <h1 className="text-2xl font-black text-white mb-4 animate-quantum-glow transform-5d">
                  CONNEXION
                </h1>
              </div>





              <form onSubmit={handleSubmit} className="space-y-4 relative z-20">
                {/* Username Field - Mobile Style */}
                <div className="relative transform-5d">
                  <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10">
                    <User className={`h-4 w-4 transition-all duration-500 ${
                      focusedField === 'identifier' ? 'text-orange-200 animate-quantum-glow' : 'text-white/60'
                    }`} />
                  </div>
                  <input
                    type="text"
                    value={identifier}
                    onChange={(e) => setIdentifier(e.target.value)}
                    onFocus={() => setFocusedField('identifier')}
                    onBlur={() => setFocusedField(null)}
                    placeholder="Nom d'utilisateur"
                    className="w-full pl-12 pr-4 py-3 bg-white/20 backdrop-blur-sm border border-white/30 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-white/50 focus:scale-105 shadow-lg transition-all duration-300 text-sm"
                    required
                  />
                  {focusedField === 'identifier' && (
                    <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-white/10 to-white/5 -z-10 blur-sm animate-quantum-glow"></div>
                  )}
                </div>

                {/* Password Field - Mobile Style */}
                <div className="relative transform-5d">
                  <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10">
                    <Lock className={`h-4 w-4 transition-all duration-500 ${
                      focusedField === 'password' ? 'text-orange-200 animate-quantum-glow' : 'text-white/60'
                    }`} />
                  </div>
                  <input
                    type={showPassword ? 'text' : 'password'}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    onFocus={() => setFocusedField('password')}
                    onBlur={() => setFocusedField(null)}
                    placeholder="Mot de passe"
                    className="w-full pl-12 pr-12 py-3 bg-white/20 backdrop-blur-sm border border-white/30 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-white/50 focus:scale-105 shadow-lg transition-all duration-300 text-sm"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute inset-y-0 right-0 pr-4 flex items-center text-white/60 hover:text-white transition-all duration-300 z-10"
                  >
                    {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </button>
                  {focusedField === 'password' && (
                    <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-white/10 to-white/5 -z-10 blur-sm animate-quantum-glow"></div>
                  )}
                </div>

                {/* Login Button - Mobile Style */}
                <button
                  type="submit"
                  disabled={isLoading}
                  className="w-full py-3 px-6 bg-white text-orange-500 font-bold text-sm rounded-xl shadow-xl hover:shadow-2xl hover:scale-105 hover:-translate-y-1 transition-all duration-300 relative overflow-hidden group disabled:opacity-50 disabled:cursor-not-allowed transform-5d"
                >
                  <span className="relative z-20 flex items-center justify-center">
                    {isLoading ? (
                      <>
                        <div className="w-4 h-4 border-2 border-orange-500 border-t-transparent rounded-full animate-spin mr-2"></div>
                        CONNEXION...
                      </>
                    ) : (
                      'SE CONNECTER'
                    )}
                  </span>
                  <div className="absolute inset-0 bg-orange-50 opacity-0 group-hover:opacity-100 transition-all duration-300"></div>
                </button>

                {/* Forgot Password Link */}
                <div className="text-center mt-4">
                  <p className="text-white/80 text-sm">
                    Vous n'avez pas de compte ?{' '}
                    <button className="text-white font-semibold underline hover:text-orange-200 transition-colors duration-300">
                      S'inscrire
                    </button>
                  </p>
                </div>


              </form>
            </div>

            {/* Security Badge */}
            <div className="flex items-center justify-center mt-6 text-gray-500">
              <Shield className="w-4 h-4 mr-2" />
              <span className="text-sm">Connexion sécurisée SSL</span>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center text-white/80 text-sm animate-fade-in-up-delay-4">
          <p>© 2024 Doorly - Votre solution moderne pour portes et fenêtres</p>
        </div>
      </div>
    </div>
  );
};

export default UltraModernLoginForm;
