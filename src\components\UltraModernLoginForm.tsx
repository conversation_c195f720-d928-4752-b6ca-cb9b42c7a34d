import React, { useState } from 'react';
import { ArrowLeft, Phone, Lock, Eye, EyeOff, User, Shield, Crown } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';

interface UltraModernLoginFormProps {
  onBack: () => void;
}

const UltraModernLoginForm: React.FC<UltraModernLoginFormProps> = ({ onBack }) => {
  const [userType, setUserType] = useState<'client' | 'admin'>('client');
  const [identifier, setIdentifier] = useState(''); // Phone for client, username for admin
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [focusedField, setFocusedField] = useState<string | null>(null);
  const { login } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    // Simulate API call
    setTimeout(() => {
      if (userType === 'admin' && identifier === 'admin' && password === 'admin123') {
        login({
          id: '1',
          email: '<EMAIL>',
          name: 'Admin Doorly',
          role: 'admin'
        });
      } else if (userType === 'client' && identifier === '********' && password === 'client123') {
        login({
          id: '2',
          email: '<EMAIL>',
          name: 'Client Doorly',
          role: 'client'
        });
      } else {
        alert('Identifiant ou mot de passe incorrect');
      }
      setIsLoading(false);
    }, 1000);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-400 via-orange-500 to-orange-600 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-20 left-10 w-32 h-32 bg-white rounded-full blur-xl animate-pulse"></div>
        <div className="absolute top-40 right-20 w-24 h-24 bg-white rounded-full blur-lg animate-bounce"></div>
        <div className="absolute bottom-32 left-1/4 w-40 h-40 bg-white rounded-full blur-2xl animate-ping"></div>
        <div className="absolute bottom-20 right-10 w-28 h-28 bg-white rounded-full blur-xl animate-pulse"></div>
      </div>

      {/* Floating Geometric Shapes */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/4 left-8 w-8 h-8 bg-white/20 rounded-lg rotate-45 animate-spin-slow"></div>
        <div className="absolute top-1/3 right-12 w-6 h-6 bg-white/30 rounded-full animate-bounce"></div>
        <div className="absolute bottom-1/3 left-16 w-10 h-10 bg-white/25 rounded-lg rotate-12 animate-pulse"></div>
        <div className="absolute top-2/3 right-1/4 w-4 h-4 bg-white/40 rounded-full animate-ping"></div>
      </div>

      <div className="flex flex-col min-h-screen px-6 py-8 relative z-10">
        {/* Header */}
        <div className="flex items-center mb-8 animate-fade-in-up">
          <button
            onClick={onBack}
            className="p-3 bg-white/20 rounded-full backdrop-blur-sm mr-4 hover:scale-110 hover:bg-white/30 transition-all duration-300 group"
          >
            <ArrowLeft className="w-6 h-6 text-white group-hover:text-orange-100" />
          </button>
          <h1 className="text-2xl font-bold text-white">Se Connecter</h1>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex items-center justify-center">
          <div className="w-full max-w-md animate-fade-in-up-delay">
            {/* Login Card */}
            <div className="bg-white rounded-3xl p-8 shadow-2xl border border-orange-200 relative overflow-hidden">
              {/* Card Background Effect */}
              <div className="absolute inset-0 bg-gradient-to-br from-orange-50 to-white rounded-3xl"></div>

              {/* Logo Doorly 3D */}
              <div className="text-center mb-6 relative z-10">
                <div className="relative w-16 h-20 mx-auto mb-4">
                  {/* Door Shadow */}
                  <div className="absolute top-1 left-1 w-full h-full bg-black/20 rounded-lg transform rotate-2 blur-sm"></div>

                  {/* Main Door */}
                  <div className="relative w-full h-full bg-gradient-to-br from-amber-100 to-amber-200 rounded-lg shadow-xl transform -rotate-1 border-2 border-amber-300">
                    {/* Door Frame */}
                    <div className="absolute inset-1 border border-amber-400 rounded-md"></div>

                    {/* Door Handle */}
                    <div className="absolute right-1 top-1/2 transform -translate-y-1/2">
                      <div className="w-1.5 h-1.5 bg-gradient-to-br from-yellow-600 to-yellow-800 rounded-full shadow-sm"></div>
                    </div>
                  </div>
                </div>

                <h1 className="text-2xl font-black text-orange-600 mb-1">DOORLY</h1>
                <h2 className="text-xl font-bold text-gray-800 mb-2">Bienvenue !</h2>
                <p className="text-gray-600">Connectez-vous à votre compte</p>
              </div>

              {/* User Type Selector */}
              <div className="flex mb-6 relative z-10 bg-orange-100 rounded-2xl p-1">
                <button
                  type="button"
                  onClick={() => setUserType('client')}
                  className={`flex-1 py-3 px-4 rounded-xl font-semibold transition-all duration-300 flex items-center justify-center ${
                    userType === 'client'
                      ? 'bg-white text-orange-600 shadow-lg'
                      : 'text-orange-700 hover:text-orange-800'
                  }`}
                >
                  <User className="w-4 h-4 mr-2" />
                  Client
                </button>
                <button
                  type="button"
                  onClick={() => setUserType('admin')}
                  className={`flex-1 py-3 px-4 rounded-xl font-semibold transition-all duration-300 flex items-center justify-center ${
                    userType === 'admin'
                      ? 'bg-white text-orange-600 shadow-lg'
                      : 'text-orange-700 hover:text-orange-800'
                  }`}
                >
                  <Crown className="w-4 h-4 mr-2" />
                  Admin
                </button>
              </div>

              <form onSubmit={handleSubmit} className="space-y-6 relative z-10">
                {/* Identifier Field */}
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                    {userType === 'client' ? (
                      <Phone className={`h-5 w-5 transition-colors duration-300 ${
                        focusedField === 'identifier' ? 'text-orange-500' : 'text-gray-400'
                      }`} />
                    ) : (
                      <User className={`h-5 w-5 transition-colors duration-300 ${
                        focusedField === 'identifier' ? 'text-orange-500' : 'text-gray-400'
                      }`} />
                    )}
                  </div>
                  <input
                    type={userType === 'client' ? 'tel' : 'text'}
                    value={identifier}
                    onChange={(e) => setIdentifier(e.target.value)}
                    onFocus={() => setFocusedField('identifier')}
                    onBlur={() => setFocusedField(null)}
                    placeholder={userType === 'client' ? 'Numéro de téléphone' : 'Nom d\'utilisateur'}
                    className="w-full pl-12 pr-4 py-4 bg-orange-50 border-2 border-orange-200 rounded-2xl text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 focus:scale-105 shadow-lg transition-all duration-300"
                    required
                  />
                  {focusedField === 'identifier' && (
                    <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-orange-400/10 to-orange-600/10 -z-10 blur-sm"></div>
                  )}
                </div>

                {/* Password Field */}
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                    <Lock className={`h-5 w-5 transition-colors duration-300 ${
                      focusedField === 'password' ? 'text-orange-500' : 'text-gray-400'
                    }`} />
                  </div>
                  <input
                    type={showPassword ? 'text' : 'password'}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    onFocus={() => setFocusedField('password')}
                    onBlur={() => setFocusedField(null)}
                    placeholder="Votre mot de passe"
                    className="w-full pl-12 pr-12 py-4 bg-orange-50 border-2 border-orange-200 rounded-2xl text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 focus:scale-105 shadow-lg transition-all duration-300"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-orange-500 transition-colors duration-300"
                  >
                    {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                  </button>
                  {focusedField === 'password' && (
                    <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-orange-400/10 to-orange-600/10 -z-10 blur-sm"></div>
                  )}
                </div>

                {/* Login Button */}
                <button
                  type="submit"
                  disabled={isLoading}
                  className="w-full py-4 px-8 bg-gradient-to-r from-orange-500 to-orange-600 text-white font-bold text-lg rounded-2xl shadow-xl hover:shadow-2xl hover:scale-105 hover:-translate-y-1 transition-all duration-300 relative overflow-hidden group disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span className="relative z-10 flex items-center justify-center">
                    {isLoading ? (
                      <>
                        <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                        Connexion...
                      </>
                    ) : (
                      'Se Connecter'
                    )}
                  </span>
                  <div className="absolute inset-0 bg-gradient-to-r from-orange-600 to-orange-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </button>

                {/* Demo Accounts */}
                <div className="mt-6 p-4 bg-orange-50 rounded-2xl border border-orange-200">
                  <p className="text-gray-700 text-sm text-center mb-3 font-semibold">Comptes de démonstration :</p>
                  <div className="space-y-2 text-xs text-gray-600">
                    <div className="flex justify-between items-center">
                      <span className="font-medium">Admin:</span>
                      <span className="bg-white px-2 py-1 rounded">admin / admin123</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="font-medium">Client:</span>
                      <span className="bg-white px-2 py-1 rounded">******** / client123</span>
                    </div>
                  </div>
                </div>
              </form>
            </div>

            {/* Security Badge */}
            <div className="flex items-center justify-center mt-6 text-gray-500">
              <Shield className="w-4 h-4 mr-2" />
              <span className="text-sm">Connexion sécurisée SSL</span>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center text-white/80 text-sm animate-fade-in-up-delay-4">
          <p>© 2024 Doorly - Votre solution moderne pour portes et fenêtres</p>
        </div>
      </div>
    </div>
  );
};

export default UltraModernLoginForm;
