<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Doorly - Mode Ho<PERSON>-ligne</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #ffffff 0%, #fff7ed 50%, #ffedd5 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            text-align: center;
            max-width: 400px;
            background: white;
            padding: 40px 30px;
            border-radius: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            border: 1px solid #fed7aa;
        }
        
        .icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #f97316, #ea580c);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 30px;
            animation: pulse 2s infinite;
        }
        
        .icon svg {
            width: 40px;
            height: 40px;
            color: white;
        }
        
        h1 {
            color: #1f2937;
            font-size: 28px;
            font-weight: 800;
            margin-bottom: 15px;
        }
        
        p {
            color: #6b7280;
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 30px;
        }
        
        .retry-btn {
            background: linear-gradient(135deg, #f97316, #ea580c);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 15px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(249, 115, 22, 0.3);
        }
        
        .retry-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(249, 115, 22, 0.4);
        }
        
        .features {
            margin-top: 30px;
            padding-top: 30px;
            border-top: 1px solid #fed7aa;
        }
        
        .feature {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
            color: #6b7280;
            font-size: 14px;
        }
        
        .feature-icon {
            width: 20px;
            height: 20px;
            margin-right: 10px;
            color: #f97316;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        @media (max-width: 480px) {
            .container {
                padding: 30px 20px;
            }
            
            h1 {
                font-size: 24px;
            }
            
            p {
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0"></path>
            </svg>
        </div>
        
        <h1>Mode Hors-ligne</h1>
        <p>Vous êtes actuellement hors-ligne. Doorly fonctionne en mode dégradé avec les données mises en cache.</p>
        
        <button class="retry-btn" onclick="window.location.reload()">
            Réessayer la connexion
        </button>
        
        <div class="features">
            <div class="feature">
                <svg class="feature-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                Catalogue produits disponible
            </div>
            <div class="feature">
                <svg class="feature-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                Données sauvegardées localement
            </div>
            <div class="feature">
                <svg class="feature-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                Synchronisation automatique
            </div>
        </div>
    </div>

    <script>
        // Vérifier la connexion périodiquement
        setInterval(() => {
            if (navigator.onLine) {
                window.location.reload();
            }
        }, 5000);

        // Écouter les changements de connexion
        window.addEventListener('online', () => {
            window.location.reload();
        });
    </script>
</body>
</html>
