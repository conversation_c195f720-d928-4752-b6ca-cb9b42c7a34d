export interface User {
  id: string;
  name: string;
  phone?: string;
  role: 'admin' | 'client';
}

export interface Product {
  id: string;
  name: string;
  price: number;
  size: string;
  color: string;
  stock: number;
  category: 'doors' | 'windows' | 'aluminum' | 'carpentry';
  image: string;
  description: string;
}

export interface Order {
  id: string;
  clientName: string;
  clientPhone: string;
  product: Product;
  quantity: number;
  totalPrice: number;
  status: 'reserved' | 'purchased';
  date: string;
  paymentMethod: string;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
}