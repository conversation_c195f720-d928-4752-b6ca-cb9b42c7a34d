import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import {
  DoorOpen as Door,
  User,
  Phone,
  Lock,
  AlertCircle,
  Eye,
  EyeOff,
  ArrowRight,
  ArrowLeft,
  Sparkles,
  Shield,
  Fingerprint,
  Smartphone,
  UserPlus,
  LogIn
} from 'lucide-react';

interface SimpleLoginFormProps {
  onBack: () => void;
}

const SimpleLoginForm: React.FC<SimpleLoginFormProps> = ({ onBack }) => {
  const [role, setRole] = useState<'admin' | 'client'>('client');
  const [identifier, setIdentifier] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [isSignUp, setIsSignUp] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const { login } = useAuth();

  useEffect(() => {
    setTimeout(() => setIsLoaded(true), 200);

    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      if (isSignUp) {
        // Logique d'inscription (simulation)
        setTimeout(() => {
          setIsSignUp(false);
          setLoading(false);
          setError('');
        }, 1500);
      } else {
        const success = await login(identifier, password, role);
        if (!success) {
          setError('Identifiants incorrects');
        }
      }
    } catch (err) {
      setError('Une erreur est survenue');
    } finally {
      if (!isSignUp) setLoading(false);
    }
  };

  return (
    <div
      className="min-h-screen flex items-center justify-center p-4 relative overflow-hidden"
      style={{
        background: `radial-gradient(circle at ${mousePosition.x}px ${mousePosition.y}px, rgba(255,165,0,0.1) 0%, transparent 50%), linear-gradient(135deg, #1a1a1a 0%, #2d1810 30%, #1a1a1a 70%, #000000 100%)`
      }}
    >
      {/* Arrière-plan ultra-moderne */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Particules flottantes */}
        {[...Array(40)].map((_, i) => (
          <div
            key={i}
            className="absolute rounded-full opacity-20 animate-float"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              width: `${Math.random() * 6 + 2}px`,
              height: `${Math.random() * 6 + 2}px`,
              background: `linear-gradient(45deg, #ff6b35, #f7931e)`,
              animationDelay: `${Math.random() * 5}s`,
              animationDuration: `${Math.random() * 3 + 2}s`
            }}
          />
        ))}

        {/* Grille géométrique */}
        <div className="absolute inset-0 opacity-10">
          <div className="grid grid-cols-12 grid-rows-12 h-full w-full">
            {[...Array(144)].map((_, i) => (
              <div key={i} className="border border-orange-500/20"></div>
            ))}
          </div>
        </div>

        {/* Cercles animés */}
        <div className="absolute top-1/4 left-1/4 w-96 h-96 border border-orange-500/20 rounded-full animate-spin-slow"></div>
        <div className="absolute bottom-1/4 right-1/4 w-64 h-64 border border-orange-400/30 rounded-full animate-spin-reverse"></div>
      </div>

      {/* Formulaire ultra-moderne */}
      <div className={`relative z-10 w-full max-w-md transform transition-all duration-1000 ${isLoaded ? 'scale-100 opacity-100 translate-y-0' : 'scale-95 opacity-0 translate-y-8'}`}>
        <div className="bg-black/40 backdrop-blur-3xl rounded-3xl p-8 shadow-2xl border border-orange-300/30 relative overflow-hidden">

          {/* Effet de lueur */}
          <div className="absolute inset-0 bg-gradient-to-br from-orange-500/10 via-transparent to-orange-600/10 rounded-3xl"></div>

          {/* Header ultra-moderne */}
          <div className="text-center mb-10 relative z-10">
            <button
              onClick={onBack}
              className="absolute top-0 left-0 p-3 hover:bg-white/10 rounded-2xl transition-all duration-300 group"
            >
              <ArrowLeft className="w-6 h-6 text-white group-hover:text-orange-400 transition-colors duration-300" />
            </button>

            {/* Logo 3D */}
            <div className="relative mx-auto mb-8">
              <div className="w-24 h-24 bg-gradient-to-br from-orange-500 via-orange-600 to-orange-700 rounded-3xl flex items-center justify-center mx-auto shadow-2xl shadow-orange-500/50 relative overflow-hidden">
                {/* Effet glassmorphism */}
                <div className="absolute inset-0 bg-gradient-to-b from-white/30 via-transparent to-black/20 rounded-3xl"></div>

                {/* Icône principale */}
                {isSignUp ? (
                  <UserPlus className="w-12 h-12 text-white relative z-10" />
                ) : (
                  <Door className="w-12 h-12 text-white relative z-10" />
                )}

                {/* Particules flottantes autour du logo */}
                <div className="absolute -top-2 -right-2 w-4 h-4 bg-yellow-400 rounded-full animate-ping"></div>
                <div className="absolute -bottom-1 -left-1 w-3 h-3 bg-orange-300 rounded-full animate-pulse"></div>
              </div>

              {/* Cercle de lueur */}
              <div className="absolute inset-0 bg-orange-500/30 rounded-full blur-2xl animate-pulse"></div>
            </div>

            <h1 className="text-4xl font-black text-white mb-3 tracking-wide">
              {isSignUp ? 'INSCRIPTION' : 'CONNEXION'}
            </h1>
            <p className="text-orange-300 font-medium text-lg">
              {isSignUp ? 'Créez votre compte Doorly' : 'Accédez à votre espace Doorly'}
            </p>

            {/* Toggle connexion/inscription */}
            <div className="mt-6">
              <button
                onClick={() => setIsSignUp(!isSignUp)}
                className="text-orange-400 hover:text-orange-300 font-bold text-sm transition-colors duration-300 underline decoration-orange-400/50 hover:decoration-orange-300"
              >
                {isSignUp ? 'Déjà un compte ? Se connecter' : 'Pas de compte ? S\'inscrire'}
              </button>
            </div>
          </div>

          {/* Sélection de rôle ultra-moderne */}
          <div className="flex mb-10 bg-black/30 backdrop-blur-xl rounded-2xl p-2 border border-white/10 relative z-10">
            <button
              type="button"
              onClick={() => setRole('client')}
              className={`flex-1 py-4 px-6 rounded-xl font-bold transition-all duration-300 relative overflow-hidden group ${
                role === 'client'
                  ? 'bg-gradient-to-r from-orange-500 to-orange-600 text-white shadow-2xl shadow-orange-500/50 transform scale-105'
                  : 'text-white/70 hover:text-white hover:bg-white/10'
              }`}
            >
              {/* Effet de lueur pour le bouton actif */}
              {role === 'client' && (
                <div className="absolute inset-0 bg-gradient-to-r from-orange-400 to-orange-600 opacity-50 blur-xl"></div>
              )}

              <div className="relative z-10 flex items-center justify-center space-x-2">
                <User className="w-5 h-5" />
                <span>Client</span>
              </div>

              {/* Particules pour le bouton actif */}
              {role === 'client' && (
                <>
                  <div className="absolute top-1 right-1 w-2 h-2 bg-yellow-400 rounded-full animate-ping"></div>
                  <div className="absolute bottom-1 left-1 w-1 h-1 bg-orange-300 rounded-full animate-pulse"></div>
                </>
              )}
            </button>

            <button
              type="button"
              onClick={() => setRole('admin')}
              className={`flex-1 py-4 px-6 rounded-xl font-bold transition-all duration-300 relative overflow-hidden group ${
                role === 'admin'
                  ? 'bg-gradient-to-r from-gray-700 to-gray-900 text-white shadow-2xl shadow-gray-700/50 transform scale-105'
                  : 'text-white/70 hover:text-white hover:bg-white/10'
              }`}
            >
              {/* Effet de lueur pour le bouton actif */}
              {role === 'admin' && (
                <div className="absolute inset-0 bg-gradient-to-r from-gray-600 to-gray-800 opacity-50 blur-xl"></div>
              )}

              <div className="relative z-10 flex items-center justify-center space-x-2">
                <Shield className="w-5 h-5" />
                <span>Admin</span>
              </div>

              {/* Particules pour le bouton actif */}
              {role === 'admin' && (
                <>
                  <div className="absolute top-1 right-1 w-2 h-2 bg-blue-400 rounded-full animate-ping"></div>
                  <div className="absolute bottom-1 left-1 w-1 h-1 bg-gray-300 rounded-full animate-pulse"></div>
                </>
              )}
            </button>
          </div>

          {/* Formulaire ultra-moderne */}
          <form onSubmit={handleSubmit} className="space-y-8 relative z-10">
            {/* Champ identifiant */}
            <div className="group">
              <label className="block text-sm font-bold text-white mb-4 tracking-wide">
                {role === 'admin' ? 'Nom d\'utilisateur' : 'Numéro de téléphone'}
              </label>
              <div className="relative">
                {/* Icône */}
                <div className="absolute left-4 top-1/2 transform -translate-y-1/2 z-10">
                  {role === 'admin' ? (
                    <User className="w-6 h-6 text-orange-400 group-focus-within:text-orange-300 transition-colors duration-300" />
                  ) : (
                    <Phone className="w-6 h-6 text-orange-400 group-focus-within:text-orange-300 transition-colors duration-300" />
                  )}
                </div>

                {/* Input avec effet glassmorphism */}
                <input
                  type={role === 'admin' ? 'text' : 'tel'}
                  value={identifier}
                  onChange={(e) => setIdentifier(e.target.value)}
                  className="w-full pl-14 pr-6 py-5 bg-black/30 backdrop-blur-xl border border-white/20 rounded-2xl focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-400 transition-all duration-300 text-white placeholder-white/50 font-medium text-lg group-focus-within:bg-black/40"
                  placeholder={role === 'admin' ? 'admin' : '+212 6XX XXX XXX'}
                  required
                />

                {/* Effet de lueur au focus */}
                <div className="absolute inset-0 bg-gradient-to-r from-orange-500/20 to-orange-600/20 rounded-2xl opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 blur-xl -z-10"></div>

                {/* Bordure animée */}
                <div className="absolute inset-0 rounded-2xl border-2 border-orange-400/0 group-focus-within:border-orange-400/50 transition-all duration-300"></div>
              </div>
            </div>

            {/* Champ mot de passe */}
            <div className="group">
              <label className="block text-sm font-bold text-white mb-4 tracking-wide">
                Mot de passe
              </label>
              <div className="relative">
                {/* Icône de verrouillage */}
                <div className="absolute left-4 top-1/2 transform -translate-y-1/2 z-10">
                  <Lock className="w-6 h-6 text-orange-400 group-focus-within:text-orange-300 transition-colors duration-300" />
                </div>

                {/* Input mot de passe avec effet glassmorphism */}
                <input
                  type={showPassword ? 'text' : 'password'}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full pl-14 pr-16 py-5 bg-black/30 backdrop-blur-xl border border-white/20 rounded-2xl focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-400 transition-all duration-300 text-white placeholder-white/50 font-medium text-lg group-focus-within:bg-black/40"
                  placeholder={role === 'admin' ? 'admin123' : 'Votre mot de passe'}
                  required
                />

                {/* Bouton afficher/masquer mot de passe */}
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-orange-400 transition-all duration-300 p-2 rounded-xl hover:bg-white/10 group"
                >
                  {showPassword ? (
                    <EyeOff className="w-6 h-6 group-hover:scale-110 transition-transform duration-300" />
                  ) : (
                    <Eye className="w-6 h-6 group-hover:scale-110 transition-transform duration-300" />
                  )}
                </button>

                {/* Effet de lueur au focus */}
                <div className="absolute inset-0 bg-gradient-to-r from-orange-500/20 to-orange-600/20 rounded-2xl opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 blur-xl -z-10"></div>

                {/* Bordure animée */}
                <div className="absolute inset-0 rounded-2xl border-2 border-orange-400/0 group-focus-within:border-orange-400/50 transition-all duration-300"></div>
              </div>
            </div>

            {/* Message d'erreur ultra-moderne */}
            {error && (
              <div className="flex items-center space-x-3 bg-red-500/20 backdrop-blur-xl border border-red-400/30 rounded-2xl p-5 animate-fade-in-up relative overflow-hidden">
                <AlertCircle className="w-6 h-6 text-red-400 animate-pulse" />
                <span className="text-red-300 font-bold text-lg">{error}</span>

                {/* Effet de lueur rouge */}
                <div className="absolute inset-0 bg-red-500/10 rounded-2xl blur-xl"></div>
              </div>
            )}

            {/* Bouton de soumission ultra-moderne */}
            <button
              type="submit"
              disabled={loading}
              className="w-full relative overflow-hidden group disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {/* Arrière-plan principal */}
              <div className="absolute inset-0 bg-gradient-to-r from-orange-500 via-orange-600 to-orange-700 rounded-2xl transition-all duration-300 group-hover:from-orange-600 group-hover:via-orange-700 group-hover:to-orange-800"></div>

              {/* Effet de lueur */}
              <div className="absolute inset-0 bg-gradient-to-r from-orange-400 to-orange-600 rounded-2xl opacity-0 group-hover:opacity-50 blur-xl transition-opacity duration-300"></div>

              {/* Particules animées */}
              <div className="absolute inset-0 overflow-hidden rounded-2xl">
                {[...Array(6)].map((_, i) => (
                  <div
                    key={i}
                    className="absolute w-2 h-2 bg-white/30 rounded-full animate-ping"
                    style={{
                      left: `${20 + i * 15}%`,
                      top: `${30 + (i % 2) * 40}%`,
                      animationDelay: `${i * 0.2}s`
                    }}
                  />
                ))}
              </div>

              {/* Contenu du bouton */}
              <div className="relative z-10 py-5 px-8 flex items-center justify-center space-x-4 transform transition-all duration-300 group-hover:scale-105">
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white"></div>
                    <span className="text-white font-black text-lg tracking-wide">
                      {isSignUp ? 'CRÉATION EN COURS...' : 'CONNEXION EN COURS...'}
                    </span>
                  </>
                ) : (
                  <>
                    <span className="text-white font-black text-lg tracking-wide">
                      {isSignUp ? 'CRÉER LE COMPTE' : 'SE CONNECTER'}
                    </span>
                    {isSignUp ? (
                      <UserPlus className="w-6 h-6 text-white group-hover:scale-110 transition-transform duration-300" />
                    ) : (
                      <LogIn className="w-6 h-6 text-white group-hover:scale-110 transition-transform duration-300" />
                    )}
                  </>
                )}
              </div>

              {/* Bordure animée */}
              <div className="absolute inset-0 rounded-2xl border-2 border-orange-400/0 group-hover:border-orange-300/50 transition-all duration-300"></div>
            </button>
          </form>

          {/* Informations de démonstration ultra-modernes */}
          <div className="mt-10 p-6 bg-black/20 backdrop-blur-xl rounded-2xl border border-orange-300/20 relative overflow-hidden">
            {/* Effet de lueur */}
            <div className="absolute inset-0 bg-gradient-to-br from-orange-500/10 to-transparent rounded-2xl"></div>

            <div className="relative z-10">
              <h4 className="text-white font-black mb-4 text-lg flex items-center space-x-2">
                <Sparkles className="w-5 h-5 text-orange-400" />
                <span>Comptes de démonstration</span>
              </h4>
              <div className="space-y-3 text-sm">
                <div className="flex items-center justify-between bg-black/30 rounded-xl p-3 border border-white/10">
                  <div className="flex items-center space-x-3">
                    <Shield className="w-4 h-4 text-blue-400" />
                    <span className="text-white font-bold">Admin</span>
                  </div>
                  <div className="text-orange-300 font-mono">admin / admin123</div>
                </div>
                <div className="flex items-center justify-between bg-black/30 rounded-xl p-3 border border-white/10">
                  <div className="flex items-center space-x-3">
                    <User className="w-4 h-4 text-green-400" />
                    <span className="text-white font-bold">Client</span>
                  </div>
                  <div className="text-orange-300 font-mono">12345678 / motdepasse</div>
                </div>
              </div>
            </div>
          </div>

          {/* Footer ultra-moderne */}
          <div className="mt-8 text-center">
            <p className="text-white/60 text-sm font-medium">
              En vous connectant, vous acceptez nos{' '}
              <span className="text-orange-400 underline decoration-orange-400/50">conditions d'utilisation</span>
            </p>
          </div>
        </div>
      </div>

      {/* Styles CSS ultra-modernes */}
      <style jsx>{`
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-15px) rotate(180deg); }
        }

        @keyframes spin-slow {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }

        @keyframes spin-reverse {
          from { transform: rotate(360deg); }
          to { transform: rotate(0deg); }
        }

        .animate-fade-in-up {
          animation: fadeInUp 0.6s ease-out;
        }

        .animate-float {
          animation: float 3s ease-in-out infinite;
        }

        .animate-spin-slow {
          animation: spin-slow 20s linear infinite;
        }

        .animate-spin-reverse {
          animation: spin-reverse 15s linear infinite;
        }
      `}</style>
    </div>
  );
};

export default SimpleLoginForm;
