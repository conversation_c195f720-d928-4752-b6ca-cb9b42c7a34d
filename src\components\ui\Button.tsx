import React, { forwardRef } from 'react';
import { clsx } from 'clsx';

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'ghost' | 'danger' | 'success';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  isLoading?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  fullWidth?: boolean;
  rounded?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
}

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      className,
      variant = 'primary',
      size = 'md',
      isLoading = false,
      leftIcon,
      rightIcon,
      fullWidth = false,
      rounded = 'xl',
      children,
      disabled,
      ...props
    },
    ref
  ) => {
    const baseClasses = [
      'inline-flex items-center justify-center font-semibold transition-all duration-300',
      'focus:outline-none focus:ring-2 focus:ring-offset-2',
      'transform hover:scale-105 active:scale-95',
      'shadow-lg hover:shadow-xl',
      'disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none',
    ];

    const variantClasses = {
      primary: [
        'bg-gradient-to-r from-orange-500 to-orange-600',
        'hover:from-orange-600 hover:to-orange-700',
        'text-white',
        'focus:ring-orange-500',
        'shadow-orange-500/25',
      ],
      secondary: [
        'bg-white hover:bg-gray-50',
        'text-gray-900',
        'border border-gray-200 hover:border-gray-300',
        'focus:ring-gray-500',
        'shadow-gray-500/10',
      ],
      ghost: [
        'bg-transparent hover:bg-gray-100',
        'text-gray-700 hover:text-gray-900',
        'focus:ring-gray-500',
        'shadow-none hover:shadow-md',
      ],
      danger: [
        'bg-gradient-to-r from-red-500 to-red-600',
        'hover:from-red-600 hover:to-red-700',
        'text-white',
        'focus:ring-red-500',
        'shadow-red-500/25',
      ],
      success: [
        'bg-gradient-to-r from-green-500 to-green-600',
        'hover:from-green-600 hover:to-green-700',
        'text-white',
        'focus:ring-green-500',
        'shadow-green-500/25',
      ],
    };

    const sizeClasses = {
      sm: 'px-3 py-2 text-sm',
      md: 'px-4 py-3 text-base',
      lg: 'px-6 py-4 text-lg',
      xl: 'px-8 py-5 text-xl',
    };

    const roundedClasses = {
      sm: 'rounded-lg',
      md: 'rounded-xl',
      lg: 'rounded-2xl',
      xl: 'rounded-3xl',
      full: 'rounded-full',
    };

    const classes = clsx(
      baseClasses,
      variantClasses[variant],
      sizeClasses[size],
      roundedClasses[rounded],
      {
        'w-full': fullWidth,
        'cursor-not-allowed': disabled || isLoading,
      },
      className
    );

    return (
      <button
        ref={ref}
        className={classes}
        disabled={disabled || isLoading}
        {...props}
      >
        {isLoading && (
          <svg
            className="animate-spin -ml-1 mr-3 h-5 w-5"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            />
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
        )}
        {!isLoading && leftIcon && <span className="mr-2">{leftIcon}</span>}
        {children}
        {!isLoading && rightIcon && <span className="ml-2">{rightIcon}</span>}
      </button>
    );
  }
);

Button.displayName = 'Button';

export default Button;
