import React, { createContext, useContext, useState, ReactNode } from 'react';
import { User, AuthState } from '../types';

interface AuthContextType extends AuthState {
  login: (phone: string, password: string, role: 'admin' | 'client') => Promise<boolean>;
  logout: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isAuthenticated: false,
  });

  const login = async (identifier: string, password: string, role: 'admin' | 'client'): Promise<boolean> => {
    // Simulation de l'authentification
    if (role === 'admin' && identifier === 'admin' && password === 'admin123') {
      const user: User = {
        id: '1',
        name: 'Administrateur Doorly',
        role: 'admin',
      };
      setAuthState({ user, isAuthenticated: true });
      return true;
    } else if (role === 'client' && identifier.length >= 8 && password.length >= 6) {
      const user: User = {
        id: '2',
        name: 'Client Doorly',
        phone: identifier,
        role: 'client',
      };
      setAuthState({ user, isAuthenticated: true });
      return true;
    }
    return false;
  };

  const logout = () => {
    setAuthState({ user: null, isAuthenticated: false });
  };

  return (
    <AuthContext.Provider value={{ ...authState, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
};