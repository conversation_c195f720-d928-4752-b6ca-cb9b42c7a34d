import React from 'react';
import { Product } from '../types';
import { 
  ArrowLeft, 
  Plus, 
  Minus, 
  Trash2, 
  ShoppingBag, 
  CreditCard,
  Tag,
  Truck
} from 'lucide-react';

interface CartPageProps {
  cart: {product: Product, quantity: number}[];
  onBack: () => void;
  onUpdateQuantity: (productId: string, quantity: number) => void;
  onRemoveItem: (productId: string) => void;
  onCheckout: () => void;
}

const CartPage: React.FC<CartPageProps> = ({ 
  cart, 
  onBack, 
  onUpdateQuantity, 
  onRemoveItem, 
  onCheckout 
}) => {
  const subtotal = cart.reduce((sum, item) => sum + (item.product.price * item.quantity), 0);
  const tax = subtotal * 0.2; // 20% TVA
  const shipping = subtotal > 2000 ? 0 : 100; // Livraison gratuite au-dessus de 2000 DH
  const total = subtotal + tax + shipping;

  if (cart.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-white via-orange-50 to-orange-100">
        {/* Header */}
        <div className="bg-white/80 backdrop-blur-xl border-b border-orange-200 sticky top-0 z-40">
          <div className="px-4 py-4">
            <div className="flex items-center space-x-3">
              <button
                onClick={onBack}
                className="p-2 hover:bg-gray-100 rounded-full transition-colors duration-200"
              >
                <ArrowLeft className="w-6 h-6 text-gray-700" />
              </button>
              <h1 className="text-xl font-bold text-gray-900">Mon Panier</h1>
            </div>
          </div>
        </div>

        {/* Empty Cart */}
        <div className="flex flex-col items-center justify-center min-h-[60vh] p-8">
          <div className="w-32 h-32 bg-gray-100 rounded-full flex items-center justify-center mb-6">
            <ShoppingBag className="w-16 h-16 text-gray-400" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Votre panier est vide</h2>
          <p className="text-gray-600 text-center mb-8">
            Découvrez nos produits et ajoutez-les à votre panier pour commencer vos achats
          </p>
          <button
            onClick={onBack}
            className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-bold py-3 px-6 rounded-2xl transition-all duration-300 transform hover:scale-105"
          >
            Continuer mes achats
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-white via-orange-50 to-orange-100">
      {/* Header */}
      <div className="bg-white/80 backdrop-blur-xl border-b border-orange-200 sticky top-0 z-40">
        <div className="px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <button
                onClick={onBack}
                className="p-2 hover:bg-gray-100 rounded-full transition-colors duration-200"
              >
                <ArrowLeft className="w-6 h-6 text-gray-700" />
              </button>
              <h1 className="text-xl font-bold text-gray-900">Mon Panier</h1>
            </div>
            <span className="bg-orange-500 text-white px-3 py-1 rounded-full text-sm font-bold">
              {cart.reduce((sum, item) => sum + item.quantity, 0)} articles
            </span>
          </div>
        </div>
      </div>

      <div className="p-4 space-y-6">
        {/* Cart Items */}
        <div className="space-y-4">
          {cart.map((item) => (
            <div key={item.product.id} className="bg-white rounded-3xl p-4 shadow-xl border border-orange-100">
              <div className="flex items-center space-x-4">
                <img 
                  src={item.product.image} 
                  alt={item.product.name}
                  className="w-20 h-20 object-cover rounded-2xl"
                />
                
                <div className="flex-1 min-w-0">
                  <h3 className="font-bold text-gray-900 truncate">{item.product.name}</h3>
                  <p className="text-sm text-gray-600">{item.product.size} • {item.product.color}</p>
                  <p className="text-lg font-bold text-orange-600">{item.product.price.toLocaleString()} DH</p>
                </div>

                <div className="flex flex-col items-end space-y-2">
                  {/* Quantity Controls */}
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => onUpdateQuantity(item.product.id, Math.max(1, item.quantity - 1))}
                      className="w-8 h-8 bg-gray-200 hover:bg-gray-300 rounded-full flex items-center justify-center transition-colors duration-200"
                    >
                      <Minus className="w-4 h-4 text-gray-700" />
                    </button>
                    <span className="w-8 text-center font-bold text-gray-900">{item.quantity}</span>
                    <button
                      onClick={() => onUpdateQuantity(item.product.id, item.quantity + 1)}
                      className="w-8 h-8 bg-gray-200 hover:bg-gray-300 rounded-full flex items-center justify-center transition-colors duration-200"
                    >
                      <Plus className="w-4 h-4 text-gray-700" />
                    </button>
                  </div>

                  {/* Remove Button */}
                  <button
                    onClick={() => onRemoveItem(item.product.id)}
                    className="p-2 text-red-500 hover:bg-red-50 rounded-full transition-colors duration-200"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Promo Code */}
        <div className="bg-white rounded-3xl p-6 shadow-xl border border-orange-100">
          <div className="flex items-center space-x-3 mb-4">
            <Tag className="w-5 h-5 text-orange-500" />
            <h3 className="font-bold text-gray-900">Code promo</h3>
          </div>
          <div className="flex space-x-3">
            <input
              type="text"
              placeholder="Entrez votre code promo"
              className="flex-1 px-4 py-3 bg-gray-50 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-orange-500 text-gray-900"
            />
            <button className="bg-orange-500 hover:bg-orange-600 text-white font-bold px-6 py-3 rounded-2xl transition-colors duration-200">
              Appliquer
            </button>
          </div>
        </div>

        {/* Order Summary */}
        <div className="bg-white rounded-3xl p-6 shadow-xl border border-orange-100">
          <h3 className="font-bold text-gray-900 mb-4">Récapitulatif de la commande</h3>
          
          <div className="space-y-3 text-gray-900">
            <div className="flex justify-between">
              <span>Sous-total</span>
              <span className="font-medium">{subtotal.toLocaleString()} DH</span>
            </div>
            <div className="flex justify-between">
              <span>TVA (20%)</span>
              <span className="font-medium">{tax.toLocaleString()} DH</span>
            </div>
            <div className="flex justify-between">
              <span className="flex items-center space-x-1">
                <Truck className="w-4 h-4" />
                <span>Livraison</span>
              </span>
              <span className="font-medium">
                {shipping === 0 ? (
                  <span className="text-green-600">Gratuite</span>
                ) : (
                  `${shipping} DH`
                )}
              </span>
            </div>
            {shipping > 0 && (
              <p className="text-sm text-gray-600">
                Livraison gratuite à partir de 2000 DH
              </p>
            )}
            <div className="border-t border-gray-200 pt-3">
              <div className="flex justify-between">
                <span className="font-bold text-lg">Total</span>
                <span className="font-bold text-lg text-orange-600">{total.toLocaleString()} DH</span>
              </div>
            </div>
          </div>
        </div>

        {/* Checkout Button */}
        <div className="sticky bottom-4">
          <button
            onClick={onCheckout}
            className="w-full bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-bold py-4 px-6 rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-2xl shadow-orange-500/25 flex items-center justify-center space-x-3"
          >
            <CreditCard className="w-5 h-5" />
            <span>Procéder au paiement</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default CartPage;
