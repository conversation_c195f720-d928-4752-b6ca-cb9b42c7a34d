import { clsx, type ClassValue } from 'clsx';

/**
 * Utility function to merge class names with clsx
 * This is a common pattern in modern React applications
 */
export function cn(...inputs: ClassValue[]) {
  return clsx(inputs);
}

/**
 * Utility function to create responsive classes
 */
export function responsive(base: string, sm?: string, md?: string, lg?: string, xl?: string) {
  return cn(
    base,
    sm && `sm:${sm}`,
    md && `md:${md}`,
    lg && `lg:${lg}`,
    xl && `xl:${xl}`
  );
}

/**
 * Utility function for conditional classes
 */
export function conditional(condition: boolean, trueClass: string, falseClass?: string) {
  return condition ? trueClass : falseClass || '';
}
