import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { products } from '../data/products';
import { Product, Order } from '../types';
import { 
  Search, 
  Filter, 
  LogOut, 
  DoorOpen as Door, 
  User, 
  Star, 
  TrendingUp, 
  Award,
  ShoppingBag,
  Heart,
  Home,
  Bell,
  Menu,
  X,
  Plus,
  Minus,
  Eye,
  Share,
  Download,
  Sparkles,
  Zap,
  Shield
} from 'lucide-react';

const ModernClientDashboard: React.FC = () => {
  const { user, logout } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showOrderModal, setShowOrderModal] = useState(false);
  const [orderType, setOrderType] = useState<'buy' | 'reserve'>('buy');
  const [orders, setOrders] = useState<Order[]>([]);
  const [activeTab, setActiveTab] = useState('home');
  const [showMobileMenu, setShowMobileMenu] = useState(false);
  const [favorites, setFavorites] = useState<string[]>([]);
  const [cart, setCart] = useState<{product: Product, quantity: number}[]>([]);

  const categories = [
    { id: 'all', name: 'Tous', icon: '🏠' },
    { id: 'doors', name: 'Portes', icon: '🚪' },
    { id: 'windows', name: 'Fenêtres', icon: '🪟' },
    { id: 'aluminum', name: 'Aluminium', icon: '⚡' },
    { id: 'carpentry', name: 'Menuiserie', icon: '🔨' }
  ];

  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const toggleFavorite = (productId: string) => {
    setFavorites(prev => 
      prev.includes(productId) 
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    );
  };

  const addToCart = (product: Product) => {
    setCart(prev => {
      const existing = prev.find(item => item.product.id === product.id);
      if (existing) {
        return prev.map(item => 
          item.product.id === product.id 
            ? { ...item, quantity: item.quantity + 1 }
            : item
        );
      }
      return [...prev, { product, quantity: 1 }];
    });
  };

  const ProductCard = ({ product }: { product: Product }) => {
    const isFavorite = favorites.includes(product.id);
    
    return (
      <div className="group bg-white/10 backdrop-blur-xl rounded-3xl p-6 border border-white/20 shadow-2xl hover:shadow-orange-500/20 transition-all duration-500 hover:scale-105 hover:bg-white/15">
        <div className="relative mb-4 overflow-hidden rounded-2xl">
          <img 
            src={product.image} 
            alt={product.name}
            className="w-full h-48 object-cover transition-transform duration-500 group-hover:scale-110"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          
          {/* Actions overlay */}
          <div className="absolute top-4 right-4 flex space-x-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <button
              onClick={() => toggleFavorite(product.id)}
              className={`p-2 rounded-full backdrop-blur-xl border border-white/20 transition-all duration-300 ${
                isFavorite ? 'bg-red-500 text-white' : 'bg-white/20 text-white hover:bg-white/30'
              }`}
            >
              <Heart className={`w-4 h-4 ${isFavorite ? 'fill-current' : ''}`} />
            </button>
            <button className="p-2 rounded-full bg-white/20 backdrop-blur-xl border border-white/20 text-white hover:bg-white/30 transition-all duration-300">
              <Eye className="w-4 h-4" />
            </button>
            <button className="p-2 rounded-full bg-white/20 backdrop-blur-xl border border-white/20 text-white hover:bg-white/30 transition-all duration-300">
              <Share className="w-4 h-4" />
            </button>
          </div>

          {/* Badge stock */}
          <div className="absolute bottom-4 left-4">
            <span className={`px-3 py-1 rounded-full text-xs font-bold ${
              product.stock > 10 
                ? 'bg-green-500/80 text-white' 
                : product.stock > 0 
                  ? 'bg-orange-500/80 text-white'
                  : 'bg-red-500/80 text-white'
            }`}>
              {product.stock > 0 ? `${product.stock} en stock` : 'Rupture'}
            </span>
          </div>
        </div>

        <div className="space-y-3">
          <div>
            <h3 className="text-lg font-bold text-white group-hover:text-orange-300 transition-colors duration-300">
              {product.name}
            </h3>
            <p className="text-gray-300 text-sm">{product.description}</p>
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <p className="text-2xl font-black text-transparent bg-gradient-to-r from-orange-400 to-pink-400 bg-clip-text">
                {product.price.toLocaleString()} DH
              </p>
              <div className="flex items-center space-x-2 text-xs text-gray-400">
                <span>{product.size}</span>
                <span>•</span>
                <span>{product.color}</span>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <div className="flex items-center space-x-1">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-3 h-3 text-yellow-400 fill-current" />
                ))}
                <span className="text-xs text-gray-400 ml-1">4.9</span>
              </div>
            </div>
          </div>

          <div className="flex space-x-2">
            <button
              onClick={() => addToCart(product)}
              className="flex-1 bg-gradient-to-r from-orange-500 to-pink-500 hover:from-orange-600 hover:to-pink-600 text-white font-bold py-3 px-4 rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-orange-500/25 flex items-center justify-center space-x-2"
            >
              <ShoppingBag className="w-4 h-4" />
              <span>Ajouter</span>
            </button>
            <button className="bg-white/10 backdrop-blur-xl border border-white/20 text-white hover:bg-white/20 font-bold py-3 px-4 rounded-2xl transition-all duration-300 transform hover:scale-105">
              <Eye className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
      {/* Background animé */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-orange-500/5 rounded-full blur-3xl animate-pulse-slow"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/5 rounded-full blur-3xl animate-pulse-slow" style={{ animationDelay: '1s' }}></div>
        <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.01)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.01)_1px,transparent_1px)] bg-[size:50px_50px]"></div>
      </div>

      {/* Header mobile */}
      <header className="relative z-50 bg-white/10 backdrop-blur-2xl border-b border-white/20 sticky top-0">
        <div className="px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-orange-500 to-pink-500 rounded-2xl flex items-center justify-center shadow-lg">
                <Door className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-black text-white">Doorly</h1>
                <p className="text-xs text-gray-300">Bonjour, {user?.name}</p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <button className="relative p-2 rounded-xl bg-white/10 backdrop-blur-xl border border-white/20 text-white hover:bg-white/20 transition-all duration-300">
                <Bell className="w-5 h-5" />
                <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
              </button>
              
              <button className="relative p-2 rounded-xl bg-white/10 backdrop-blur-xl border border-white/20 text-white hover:bg-white/20 transition-all duration-300">
                <ShoppingBag className="w-5 h-5" />
                {cart.length > 0 && (
                  <span className="absolute -top-1 -right-1 w-5 h-5 bg-orange-500 rounded-full flex items-center justify-center text-xs font-bold text-white">
                    {cart.reduce((sum, item) => sum + item.quantity, 0)}
                  </span>
                )}
              </button>

              <button
                onClick={() => setShowMobileMenu(!showMobileMenu)}
                className="p-2 rounded-xl bg-white/10 backdrop-blur-xl border border-white/20 text-white hover:bg-white/20 transition-all duration-300"
              >
                {showMobileMenu ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
              </button>
            </div>
          </div>

          {/* Barre de recherche */}
          <div className="mt-4">
            <div className="relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="text"
                placeholder="Rechercher des produits..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-12 pr-4 py-3 bg-white/10 backdrop-blur-xl border border-white/20 rounded-2xl text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-500 transition-all duration-300"
              />
            </div>
          </div>

          {/* Catégories */}
          <div className="mt-4 flex space-x-2 overflow-x-auto pb-2">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`flex items-center space-x-2 px-4 py-2 rounded-2xl font-medium transition-all duration-300 whitespace-nowrap ${
                  selectedCategory === category.id
                    ? 'bg-gradient-to-r from-orange-500 to-pink-500 text-white shadow-lg'
                    : 'bg-white/10 backdrop-blur-xl border border-white/20 text-gray-300 hover:text-white hover:bg-white/20'
                }`}
              >
                <span>{category.icon}</span>
                <span>{category.name}</span>
              </button>
            ))}
          </div>
        </div>
      </header>

      {/* Menu mobile */}
      {showMobileMenu && (
        <div className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm">
          <div className="absolute right-0 top-0 h-full w-80 bg-white/10 backdrop-blur-2xl border-l border-white/20 p-6">
            <div className="flex items-center justify-between mb-8">
              <h2 className="text-xl font-bold text-white">Menu</h2>
              <button
                onClick={() => setShowMobileMenu(false)}
                className="p-2 rounded-xl bg-white/10 backdrop-blur-xl border border-white/20 text-white hover:bg-white/20 transition-all duration-300"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <div className="space-y-4">
              <button className="w-full flex items-center space-x-3 p-4 rounded-2xl bg-white/10 backdrop-blur-xl border border-white/20 text-white hover:bg-white/20 transition-all duration-300">
                <User className="w-5 h-5" />
                <span>Mon Profil</span>
              </button>
              <button className="w-full flex items-center space-x-3 p-4 rounded-2xl bg-white/10 backdrop-blur-xl border border-white/20 text-white hover:bg-white/20 transition-all duration-300">
                <ShoppingBag className="w-5 h-5" />
                <span>Mes Commandes</span>
              </button>
              <button className="w-full flex items-center space-x-3 p-4 rounded-2xl bg-white/10 backdrop-blur-xl border border-white/20 text-white hover:bg-white/20 transition-all duration-300">
                <Heart className="w-5 h-5" />
                <span>Favoris</span>
              </button>
              <button
                onClick={logout}
                className="w-full flex items-center space-x-3 p-4 rounded-2xl bg-red-500/20 backdrop-blur-xl border border-red-500/30 text-red-300 hover:bg-red-500/30 transition-all duration-300"
              >
                <LogOut className="w-5 h-5" />
                <span>Déconnexion</span>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Contenu principal */}
      <main className="relative z-10 p-4 pb-24">
        {/* Stats rapides */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <div className="bg-white/10 backdrop-blur-xl rounded-2xl p-4 border border-white/20">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center">
                <Shield className="w-5 h-5 text-white" />
              </div>
              <div>
                <p className="text-white font-bold">{products.length}</p>
                <p className="text-gray-300 text-xs">Produits</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white/10 backdrop-blur-xl rounded-2xl p-4 border border-white/20">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-orange-500 to-pink-500 rounded-xl flex items-center justify-center">
                <Heart className="w-5 h-5 text-white" />
              </div>
              <div>
                <p className="text-white font-bold">{favorites.length}</p>
                <p className="text-gray-300 text-xs">Favoris</p>
              </div>
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-xl rounded-2xl p-4 border border-white/20">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center">
                <ShoppingBag className="w-5 h-5 text-white" />
              </div>
              <div>
                <p className="text-white font-bold">{cart.reduce((sum, item) => sum + item.quantity, 0)}</p>
                <p className="text-gray-300 text-xs">Panier</p>
              </div>
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-xl rounded-2xl p-4 border border-white/20">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-xl flex items-center justify-center">
                <Star className="w-5 h-5 text-white" />
              </div>
              <div>
                <p className="text-white font-bold">4.9</p>
                <p className="text-gray-300 text-xs">Note</p>
              </div>
            </div>
          </div>
        </div>

        {/* Grille de produits */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredProducts.map((product) => (
            <ProductCard key={product.id} product={product} />
          ))}
        </div>

        {filteredProducts.length === 0 && (
          <div className="text-center py-12">
            <div className="w-24 h-24 bg-white/10 backdrop-blur-xl rounded-3xl flex items-center justify-center mx-auto mb-4">
              <Search className="w-12 h-12 text-gray-400" />
            </div>
            <h3 className="text-xl font-bold text-white mb-2">Aucun produit trouvé</h3>
            <p className="text-gray-300">Essayez de modifier vos critères de recherche</p>
          </div>
        )}
      </main>

      {/* Navigation mobile bottom */}
      <div className="fixed bottom-0 left-0 right-0 z-50 bg-white/10 backdrop-blur-2xl border-t border-white/20 p-4">
        <div className="flex items-center justify-around">
          <button className="flex flex-col items-center space-y-1 p-2 rounded-xl bg-gradient-to-r from-orange-500 to-pink-500 text-white">
            <Home className="w-5 h-5" />
            <span className="text-xs font-medium">Accueil</span>
          </button>
          <button className="flex flex-col items-center space-y-1 p-2 text-gray-400 hover:text-white transition-colors duration-300">
            <Search className="w-5 h-5" />
            <span className="text-xs font-medium">Recherche</span>
          </button>
          <button className="flex flex-col items-center space-y-1 p-2 text-gray-400 hover:text-white transition-colors duration-300 relative">
            <ShoppingBag className="w-5 h-5" />
            <span className="text-xs font-medium">Panier</span>
            {cart.length > 0 && (
              <span className="absolute -top-1 -right-1 w-4 h-4 bg-orange-500 rounded-full flex items-center justify-center text-xs font-bold text-white">
                {cart.reduce((sum, item) => sum + item.quantity, 0)}
              </span>
            )}
          </button>
          <button className="flex flex-col items-center space-y-1 p-2 text-gray-400 hover:text-white transition-colors duration-300">
            <Heart className="w-5 h-5" />
            <span className="text-xs font-medium">Favoris</span>
          </button>
          <button className="flex flex-col items-center space-y-1 p-2 text-gray-400 hover:text-white transition-colors duration-300">
            <User className="w-5 h-5" />
            <span className="text-xs font-medium">Profil</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default ModernClientDashboard;
