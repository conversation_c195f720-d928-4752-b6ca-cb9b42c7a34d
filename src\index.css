@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Animations ultra-modernes pour la page d'accueil */

/* Animations de base */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes float-3d {
  0%, 100% {
    transform: translateY(0px) translateZ(0px) rotateY(0deg);
  }
  33% {
    transform: translateY(-15px) translateZ(20px) rotateY(120deg);
  }
  66% {
    transform: translateY(-10px) translateZ(-10px) rotateY(240deg);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    opacity: 0.4;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.1);
  }
}

@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes scan {
  0% {
    top: 0%;
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
  100% {
    top: 100%;
    opacity: 0;
  }
}

@keyframes scan-horizontal {
  0% {
    left: -100%;
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    left: 100%;
    opacity: 0;
  }
}

@keyframes scan-vertical {
  0% {
    top: -100%;
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    top: 100%;
    opacity: 0;
  }
}

@keyframes orbit {
  from {
    transform: rotate(0deg) translateX(var(--orbit-radius, 60px)) rotate(0deg);
  }
  to {
    transform: rotate(360deg) translateX(var(--orbit-radius, 60px)) rotate(-360deg);
  }
}

@keyframes text-glow {
  0%, 100% {
    text-shadow: 0 0 20px currentColor;
  }
  50% {
    text-shadow: 0 0 30px currentColor, 0 0 40px currentColor;
  }
}

@keyframes letter-float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}

@keyframes typewriter {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

@keyframes float-5d {
  0%, 100% {
    transform: translateY(0px) translateZ(0px) rotateX(0deg) rotateY(0deg);
  }
  25% {
    transform: translateY(-20px) translateZ(30px) rotateX(15deg) rotateY(90deg);
  }
  50% {
    transform: translateY(-10px) translateZ(-20px) rotateX(-10deg) rotateY(180deg);
  }
  75% {
    transform: translateY(-15px) translateZ(40px) rotateX(20deg) rotateY(270deg);
  }
}

@keyframes pulse-wave {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.5;
  }
  100% {
    transform: scale(1.8);
    opacity: 0;
  }
}

@keyframes float-particle {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-15px) translateX(5px);
    opacity: 1;
  }
}

@keyframes quantum-spin {
  0% {
    transform: rotateX(0deg) rotateY(0deg) rotateZ(0deg);
  }
  33% {
    transform: rotateX(120deg) rotateY(120deg) rotateZ(120deg);
  }
  66% {
    transform: rotateX(240deg) rotateY(240deg) rotateZ(240deg);
  }
  100% {
    transform: rotateX(360deg) rotateY(360deg) rotateZ(360deg);
  }
}

@keyframes hologram-flicker {
  0%, 100% {
    opacity: 1;
    filter: brightness(1);
  }
  50% {
    opacity: 0.8;
    filter: brightness(1.2);
  }
  75% {
    opacity: 0.9;
    filter: brightness(0.9);
  }
}

@keyframes energy-flow {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Classes d'animation */
.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-float-3d {
  animation: float-3d 8s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 3s ease-in-out infinite;
}

.animate-spin-slow {
  animation: spin-slow 8s linear infinite;
}

.animate-shimmer {
  animation: shimmer 2s ease-in-out infinite;
}

.animate-scan {
  animation: scan 3s ease-in-out infinite;
}

.animate-scan-horizontal {
  animation: scan-horizontal 4s ease-in-out infinite;
}

.animate-scan-vertical {
  animation: scan-vertical 3s ease-in-out infinite;
}

.animate-orbit {
  animation: orbit 4s linear infinite;
}

.animate-text-glow {
  animation: text-glow 2s ease-in-out infinite;
}

.animate-letter-float {
  animation: letter-float 2s ease-in-out infinite;
}

.animate-typewriter {
  animation: typewriter 2s steps(20) forwards;
  overflow: hidden;
  white-space: nowrap;
}

.animate-blink {
  animation: blink 1s infinite;
}

.animate-float-5d {
  animation: float-5d 10s ease-in-out infinite;
}

.animate-pulse-wave {
  animation: pulse-wave 3s ease-out infinite;
}

.animate-float-particle {
  animation: float-particle 4s ease-in-out infinite;
}

.animate-quantum-spin {
  animation: quantum-spin 20s linear infinite;
}

.animate-hologram-flicker {
  animation: hologram-flicker 2s ease-in-out infinite;
}

.animate-energy-flow {
  animation: energy-flow 3s ease-in-out infinite;
  background-size: 200% 200%;
}

.animate-fade-in-up {
  animation: fade-in-up 0.6s ease-out forwards;
}

.animate-fade-in-up-delay {
  animation: fade-in-up 0.8s ease-out 0.3s forwards;
  opacity: 0;
}

.animate-fade-in-up-delay-2 {
  animation: fade-in-up 0.8s ease-out 0.6s forwards;
  opacity: 0;
}

.animate-fade-in-up-delay-3 {
  animation: fade-in-up 0.8s ease-out 0.9s forwards;
  opacity: 0;
}

.animate-fade-in-up-delay-4 {
  animation: fade-in-up 0.8s ease-out 1.2s forwards;
  opacity: 0;
}

.animate-spin-slow {
  animation: spin 8s linear infinite;
}

/* Effets 5D Ultra-Modernes */
@keyframes float-5d-ultra {
  0%, 100% {
    transform: translateY(0px) translateZ(0px) rotateX(0deg) rotateY(0deg) rotateZ(0deg);
    filter: blur(0px) brightness(1) saturate(1);
  }
  25% {
    transform: translateY(-20px) translateZ(30px) rotateX(15deg) rotateY(90deg) rotateZ(5deg);
    filter: blur(1px) brightness(1.1) saturate(1.2);
  }
  50% {
    transform: translateY(-10px) translateZ(-20px) rotateX(-10deg) rotateY(180deg) rotateZ(-3deg);
    filter: blur(2px) brightness(0.9) saturate(1.1);
  }
  75% {
    transform: translateY(-15px) translateZ(40px) rotateX(20deg) rotateY(270deg) rotateZ(8deg);
    filter: blur(1px) brightness(1.2) saturate(1.3);
  }
}

@keyframes glassmorphism-5d {
  0%, 100% {
    backdrop-filter: blur(20px) saturate(180%);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  }
  50% {
    backdrop-filter: blur(30px) saturate(200%);
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 12px 40px 0 rgba(31, 38, 135, 0.5);
  }
}

@keyframes holographic-shift {
  0% {
    background: linear-gradient(45deg, rgba(255, 165, 0, 0.1), rgba(255, 140, 0, 0.1));
    transform: perspective(1000px) rotateY(0deg);
  }
  25% {
    background: linear-gradient(45deg, rgba(255, 140, 0, 0.15), rgba(255, 69, 0, 0.1));
    transform: perspective(1000px) rotateY(5deg);
  }
  50% {
    background: linear-gradient(45deg, rgba(255, 69, 0, 0.1), rgba(255, 165, 0, 0.15));
    transform: perspective(1000px) rotateY(0deg);
  }
  75% {
    background: linear-gradient(45deg, rgba(255, 165, 0, 0.15), rgba(255, 140, 0, 0.1));
    transform: perspective(1000px) rotateY(-5deg);
  }
  100% {
    background: linear-gradient(45deg, rgba(255, 165, 0, 0.1), rgba(255, 140, 0, 0.1));
    transform: perspective(1000px) rotateY(0deg);
  }
}

@keyframes depth-field-blur {
  0%, 100% {
    filter: blur(0px) brightness(1) contrast(1);
    transform: translateZ(0px);
  }
  50% {
    filter: blur(3px) brightness(1.1) contrast(1.2);
    transform: translateZ(20px);
  }
}

@keyframes quantum-glow {
  0%, 100% {
    box-shadow:
      0 0 20px rgba(255, 165, 0, 0.3),
      0 0 40px rgba(255, 140, 0, 0.2),
      0 0 60px rgba(255, 69, 0, 0.1),
      inset 0 0 20px rgba(255, 255, 255, 0.1);
  }
  50% {
    box-shadow:
      0 0 30px rgba(255, 165, 0, 0.5),
      0 0 60px rgba(255, 140, 0, 0.4),
      0 0 90px rgba(255, 69, 0, 0.3),
      inset 0 0 30px rgba(255, 255, 255, 0.2);
  }
}

/* Classes d'animation 5D */
.animate-float-5d-ultra {
  animation: float-5d-ultra 12s ease-in-out infinite;
}

.animate-glassmorphism-5d {
  animation: glassmorphism-5d 4s ease-in-out infinite;
}

.animate-holographic-shift {
  animation: holographic-shift 6s ease-in-out infinite;
}

.animate-depth-field-blur {
  animation: depth-field-blur 3s ease-in-out infinite;
}

.animate-quantum-glow {
  animation: quantum-glow 2s ease-in-out infinite;
}

/* Effets de perspective 5D */
.perspective-5d {
  perspective: 2000px;
  transform-style: preserve-3d;
}

.transform-5d {
  transform-style: preserve-3d;
  transition: all 0.6s cubic-bezier(0.23, 1, 0.320, 1);
}

.transform-5d:hover {
  transform:
    perspective(2000px)
    rotateX(10deg)
    rotateY(10deg)
    translateZ(20px)
    scale(1.05);
}

/* Glassmorphism ultra-avancé */
.glass-5d {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(30px) saturate(200%);
  -webkit-backdrop-filter: blur(30px) saturate(200%);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 8px 32px 0 rgba(31, 38, 135, 0.37),
    inset 0 1px 0 rgba(255, 255, 255, 0.3),
    inset 0 -1px 0 rgba(255, 255, 255, 0.1);
}

.glass-5d-orange {
  background: rgba(255, 165, 0, 0.1);
  backdrop-filter: blur(25px) saturate(180%) hue-rotate(10deg);
  -webkit-backdrop-filter: blur(25px) saturate(180%) hue-rotate(10deg);
  border: 1px solid rgba(255, 140, 0, 0.3);
  box-shadow:
    0 12px 40px 0 rgba(255, 69, 0, 0.3),
    inset 0 2px 0 rgba(255, 255, 255, 0.4),
    inset 0 -2px 0 rgba(255, 140, 0, 0.2);
}

/* Utilitaires 3D */
.perspective-1000 {
  perspective: 1000px;
}

.perspective-2000 {
  perspective: 2000px;
}

.transform-3d {
  transform-style: preserve-3d;
}

.rotate-y-12 {
  transform: rotateY(12deg);
}

.rotate-y-6 {
  transform: rotateY(6deg);
}

.skew-y-12 {
  transform: skewY(12deg);
}

/* Effets de glassmorphism */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Optimisations mobiles */
.safe-area-top {
  padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

.safe-area-left {
  padding-left: env(safe-area-inset-left);
}

.safe-area-right {
  padding-right: env(safe-area-inset-right);
}

/* Responsive mobile optimizations */
@media (max-width: 640px) {
  .mobile-optimized {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .mobile-padding {
    padding: 0.75rem;
  }

  .mobile-margin {
    margin: 0.5rem;
  }
}

/* Touch optimizations */
@media (hover: none) and (pointer: coarse) {
  .touch-optimized {
    min-height: 44px;
    min-width: 44px;
  }
}

/* Variables CSS pour le thème */
:root {
  --primary-50: #fff7ed;
  --primary-100: #ffedd5;
  --primary-200: #fed7aa;
  --primary-300: #fdba74;
  --primary-400: #fb923c;
  --primary-500: #f97316;
  --primary-600: #ea580c;
  --primary-700: #c2410c;
  --primary-800: #9a3412;
  --primary-900: #7c2d12;

  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);

  --gradient-primary: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
  --gradient-secondary: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  --gradient-success: linear-gradient(135deg, #10b981 0%, #059669 100%);
  --gradient-dark: linear-gradient(135deg, #1f2937 0%, #111827 100%);
}

[data-theme="dark"] {
  --glass-bg: rgba(0, 0, 0, 0.3);
  --glass-border: rgba(255, 255, 255, 0.1);
}

/* Base styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: linear-gradient(135deg, #ffffff 0%, #fff7ed 50%, #ffedd5 100%);
  min-height: 100vh;
}

/* Animations personnalisées */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* Classes utilitaires personnalisées */
.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-scale {
  animation: fadeInScale 0.4s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.5s ease-out;
}

.animate-pulse-slow {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Effet glassmorphism */
.glass {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
}

/* Gradients */
.gradient-primary {
  background: var(--gradient-primary);
}

.gradient-secondary {
  background: var(--gradient-secondary);
}

.gradient-success {
  background: var(--gradient-success);
}

.gradient-dark {
  background: var(--gradient-dark);
}

/* Effet de shimmer pour le loading */
.shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Scrollbar personnalisée */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #f97316, #ea580c);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #ea580c, #c2410c);
}

/* Styles pour mobile */
@media (max-width: 768px) {
  .mobile-padding {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .mobile-text {
    font-size: 0.875rem;
  }
}

/* Effet de focus moderne */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 focus:ring-offset-white;
}

/* Boutons modernes */
.btn-primary {
  @apply bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-semibold py-3 px-6 rounded-2xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 focus-ring;
}

.btn-secondary {
  @apply bg-white hover:bg-gray-50 text-gray-900 font-semibold py-3 px-6 rounded-2xl shadow-lg hover:shadow-xl border border-gray-200 transform hover:scale-105 transition-all duration-300 focus-ring;
}

.btn-ghost {
  @apply bg-transparent hover:bg-gray-100 text-gray-700 font-semibold py-3 px-6 rounded-2xl transition-all duration-300 focus-ring;
}

/* Cards modernes */
.card-modern {
  @apply bg-white rounded-3xl shadow-xl hover:shadow-2xl border border-gray-100 transition-all duration-300 hover:scale-105;
}

.card-glass {
  @apply glass rounded-3xl transition-all duration-300 hover:scale-105;
}

/* Inputs modernes */
.input-modern {
  @apply w-full px-4 py-3 rounded-2xl border border-gray-200 focus:border-orange-500 focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 transition-all duration-300 bg-white shadow-sm;
}
