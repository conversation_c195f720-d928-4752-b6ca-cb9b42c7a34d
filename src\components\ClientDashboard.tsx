import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { products } from '../data/products';
import { Product, Order } from '../types';
import ProductCard from './ProductCard';
import ProductDetailModal from './ProductDetailModal';
import OrderModal from './OrderModal';
import { Search, Filter, LogOut, DoorOpen as Door, User, Star, TrendingUp, Award } from 'lucide-react';

const ClientDashboard: React.FC = () => {
  const { user, logout } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showOrderModal, setShowOrderModal] = useState(false);
  const [orderType, setOrderType] = useState<'buy' | 'reserve'>('buy');
  const [orders, setOrders] = useState<Order[]>([]);

  const categories = [
    { id: 'all', name: 'Tous les produits' },
    { id: 'doors', name: 'Portes' },
    { id: 'windows', name: 'Fenêtres' },
    { id: 'aluminum', name: 'Aluminium' },
    { id: 'carpentry', name: 'Menuiserie' }
  ];

  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const handleProductClick = (product: Product) => {
    setSelectedProduct(product);
    setShowDetailModal(true);
  };

  const handleBuy = (product: Product) => {
    setSelectedProduct(product);
    setOrderType('buy');
    setShowDetailModal(false);
    setShowOrderModal(true);
  };

  const handleReserve = (product: Product) => {
    setSelectedProduct(product);
    setOrderType('reserve');
    setShowDetailModal(false);
    setShowOrderModal(true);
  };

  const handleOrderConfirm = (orderData: Omit<Order, 'id' | 'date'>) => {
    const newOrder: Order = {
      ...orderData,
      id: Date.now().toString(),
      date: new Date().toLocaleDateString('fr-FR')
    };
    setOrders([...orders, newOrder]);
  };

  const handleCloseModals = () => {
    setSelectedProduct(null);
    setShowDetailModal(false);
    setShowOrderModal(false);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-orange-50">
      {/* Header */}
      <header className="bg-white/95 backdrop-blur-xl shadow-xl border-b border-orange-200 sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-20">
            <div className="flex items-center space-x-4">
              <div className="flex items-center justify-center w-14 h-14 bg-gradient-to-r from-orange-500 to-orange-600 rounded-2xl shadow-lg overflow-hidden">
                <img 
                  src="/278426439_103701565654071_785914601533428886_n copy.jpg" 
                  alt="Doorly Logo" 
                  className="w-full h-full object-cover"
                />
              </div>
              <div>
                <h1 className="text-3xl font-bold bg-gradient-to-r from-orange-600 to-orange-800 bg-clip-text text-transparent">DOORLY</h1>
                <p className="text-sm text-gray-600 font-medium">Catalogue Premium</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="hidden md:flex items-center space-x-6 text-sm">
                <div className="flex items-center space-x-2 text-gray-600">
                  <Star className="w-4 h-4 text-orange-500" />
                  <span className="font-medium">Qualité Premium</span>
                </div>
                <div className="flex items-center space-x-2 text-gray-600">
                  <Award className="w-4 h-4 text-orange-500" />
                  <span className="font-medium">Garantie 10 ans</span>
                </div>
              </div>
              <div className="flex items-center space-x-2 text-gray-800">
                <User className="w-5 h-5" />
                <span className="font-semibold">{user?.name}</span>
              </div>
              <button
                onClick={logout}
                className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-gray-800 to-black hover:from-gray-900 hover:to-gray-800 text-white rounded-xl transition-all duration-200 shadow-lg"
              >
                <LogOut className="w-4 h-4" />
                <span className="font-medium">Déconnexion</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Hero Section */}
        <div className="bg-gradient-to-r from-orange-500 to-orange-600 rounded-3xl p-8 mb-8 text-white shadow-2xl">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-4xl font-bold mb-2">Découvrez Notre Collection Premium</h2>
              <p className="text-orange-100 mb-4 text-lg">Portes et fenêtres de haute qualité pour votre maison</p>
              <div className="flex items-center space-x-6 text-sm">
                <div className="flex items-center space-x-2">
                  <TrendingUp className="w-5 h-5" />
                  <span className="font-medium">+500 clients satisfaits</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Award className="w-5 h-5" />
                  <span className="font-medium">Certification ISO 9001</span>
                </div>
              </div>
            </div>
            <div className="hidden lg:block">
              <div className="w-32 h-32 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                <Door className="w-16 h-16 text-white" />
              </div>
            </div>
          </div>
        </div>

        {/* Search and Filter */}
        <div className="mb-8 space-y-4">
          <div className="relative">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-orange-500" />
            <input
              type="text"
              placeholder="Rechercher un produit..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-12 pr-4 py-4 bg-white rounded-2xl shadow-lg border-2 border-orange-200 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-300 text-gray-900 placeholder-gray-500"
            />
          </div>

          <div className="flex items-center space-x-2 overflow-x-auto pb-2">
            <Filter className="w-5 h-5 text-orange-600 flex-shrink-0" />
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`px-6 py-3 rounded-2xl font-semibold transition-all duration-300 whitespace-nowrap ${
                  selectedCategory === category.id
                    ? 'bg-gradient-to-r from-orange-500 to-orange-600 text-white shadow-lg transform scale-105'
                    : 'bg-white text-gray-700 hover:bg-orange-50 shadow-md border-2 border-orange-200'
                }`}
              >
                {category.name}
              </button>
            ))}
          </div>
        </div>

        {/* Products Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredProducts.map((product) => (
            <ProductCard
              key={product.id}
              product={product}
              onProductClick={handleProductClick}
              onBuy={handleBuy}
              onReserve={handleReserve}
            />
          ))}
        </div>

        {filteredProducts.length === 0 && (
          <div className="text-center py-16">
            <div className="w-24 h-24 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Search className="w-12 h-12 text-orange-500" />
            </div>
            <h3 className="text-xl font-semibold text-gray-700 mb-2">Aucun produit trouvé</h3>
            <p className="text-gray-500">Essayez de modifier vos critères de recherche</p>
          </div>
        )}
      </div>

      {/* Product Detail Modal */}
      {selectedProduct && showDetailModal && (
        <ProductDetailModal
          product={selectedProduct}
          onClose={handleCloseModals}
          onBuy={handleBuy}
          onReserve={handleReserve}
        />
      )}

      {/* Order Modal */}
      {selectedProduct && showOrderModal && (
        <OrderModal
          product={selectedProduct}
          type={orderType}
          onClose={handleCloseModals}
          onConfirm={handleOrderConfirm}
        />
      )}
    </div>
  );
};

export default ClientDashboard;