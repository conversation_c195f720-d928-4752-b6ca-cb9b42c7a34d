import React from 'react';
import { Product } from '../types';
import { 
  ArrowLeft, 
  Heart, 
  ShoppingBag, 
  Star,
  Share,
  Eye
} from 'lucide-react';

interface FavoritesPageProps {
  favorites: string[];
  products: Product[];
  onBack: () => void;
  onToggleFavorite: (productId: string) => void;
  onAddToCart: (product: Product) => void;
  onBuyProduct: (product: Product) => void;
}

const FavoritesPage: React.FC<FavoritesPageProps> = ({ 
  favorites, 
  products, 
  onBack, 
  onToggleFavorite, 
  onAddToCart,
  onBuyProduct 
}) => {
  const favoriteProducts = products.filter(product => favorites.includes(product.id));

  if (favoriteProducts.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-white via-orange-50 to-orange-100">
        {/* Header */}
        <div className="bg-white/80 backdrop-blur-xl border-b border-orange-200 sticky top-0 z-40">
          <div className="px-4 py-4">
            <div className="flex items-center space-x-3">
              <button
                onClick={onBack}
                className="p-2 hover:bg-gray-100 rounded-full transition-colors duration-200"
              >
                <ArrowLeft className="w-6 h-6 text-gray-700" />
              </button>
              <h1 className="text-xl font-bold text-gray-900">Mes Favoris</h1>
            </div>
          </div>
        </div>

        {/* Empty Favorites */}
        <div className="flex flex-col items-center justify-center min-h-[60vh] p-8">
          <div className="w-32 h-32 bg-gray-100 rounded-full flex items-center justify-center mb-6">
            <Heart className="w-16 h-16 text-gray-400" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Aucun favori pour le moment</h2>
          <p className="text-gray-600 text-center mb-8">
            Ajoutez des produits à vos favoris en appuyant sur le cœur pour les retrouver facilement
          </p>
          <button
            onClick={onBack}
            className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-bold py-3 px-6 rounded-2xl transition-all duration-300 transform hover:scale-105"
          >
            Découvrir nos produits
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-white via-orange-50 to-orange-100">
      {/* Header */}
      <div className="bg-white/80 backdrop-blur-xl border-b border-orange-200 sticky top-0 z-40">
        <div className="px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <button
                onClick={onBack}
                className="p-2 hover:bg-gray-100 rounded-full transition-colors duration-200"
              >
                <ArrowLeft className="w-6 h-6 text-gray-700" />
              </button>
              <h1 className="text-xl font-bold text-gray-900">Mes Favoris</h1>
            </div>
            <span className="bg-red-500 text-white px-3 py-1 rounded-full text-sm font-bold">
              {favoriteProducts.length} produits
            </span>
          </div>
        </div>
      </div>

      <div className="p-4">
        {/* Favorites Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {favoriteProducts.map((product) => (
            <div key={product.id} className="bg-white rounded-3xl p-6 shadow-xl border border-orange-100 hover:shadow-2xl transition-all duration-300 hover:scale-105">
              <div className="relative mb-4">
                <img 
                  src={product.image} 
                  alt={product.name}
                  className="w-full h-48 object-cover rounded-2xl"
                />
                
                {/* Action Buttons Overlay */}
                <div className="absolute top-3 right-3 flex flex-col space-y-2">
                  <button
                    onClick={() => onToggleFavorite(product.id)}
                    className="p-2 bg-white/90 backdrop-blur-xl rounded-full hover:bg-white transition-all duration-300 shadow-lg"
                  >
                    <Heart className="w-4 h-4 text-red-500 fill-current" />
                  </button>
                  <button className="p-2 bg-white/90 backdrop-blur-xl rounded-full hover:bg-white transition-all duration-300 shadow-lg">
                    <Share className="w-4 h-4 text-gray-600" />
                  </button>
                  <button className="p-2 bg-white/90 backdrop-blur-xl rounded-full hover:bg-white transition-all duration-300 shadow-lg">
                    <Eye className="w-4 h-4 text-gray-600" />
                  </button>
                </div>

                {/* Stock Badge */}
                <div className="absolute bottom-3 left-3">
                  <span className={`px-3 py-1 rounded-full text-xs font-bold ${
                    product.stock > 10 
                      ? 'bg-green-500/90 text-white' 
                      : product.stock > 0 
                        ? 'bg-orange-500/90 text-white'
                        : 'bg-red-500/90 text-white'
                  }`}>
                    {product.stock > 0 ? `${product.stock} en stock` : 'Rupture'}
                  </span>
                </div>
              </div>

              <div className="space-y-3">
                <div>
                  <h3 className="text-lg font-bold text-gray-900">{product.name}</h3>
                  <p className="text-gray-600 text-sm">{product.description}</p>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-2xl font-black text-orange-600">{product.price.toLocaleString()} DH</p>
                    <div className="flex items-center space-x-2 text-xs text-gray-500">
                      <span>{product.size}</span>
                      <span>•</span>
                      <span>{product.color}</span>
                    </div>
                  </div>

                  <div className="text-right">
                    <div className="flex items-center space-x-1 mb-1">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="w-3 h-3 text-yellow-400 fill-current" />
                      ))}
                    </div>
                    <span className="text-xs text-gray-500">4.9/5</span>
                  </div>
                </div>

                <div className="flex space-x-2">
                  <button
                    onClick={() => onAddToCart(product)}
                    className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-800 font-bold py-3 px-4 rounded-2xl transition-all duration-300 transform hover:scale-105 flex items-center justify-center space-x-2"
                  >
                    <ShoppingBag className="w-4 h-4" />
                    <span>Panier</span>
                  </button>
                  <button
                    onClick={() => onBuyProduct(product)}
                    className="flex-1 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-bold py-3 px-4 rounded-2xl transition-all duration-300 transform hover:scale-105 flex items-center justify-center space-x-2"
                  >
                    <span>Acheter</span>
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Quick Actions */}
        <div className="mt-8 bg-white rounded-3xl p-6 shadow-xl border border-orange-100">
          <h3 className="text-lg font-bold text-gray-900 mb-4">Actions rapides</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button
              onClick={() => {
                favoriteProducts.forEach(product => onAddToCart(product));
              }}
              className="bg-blue-50 hover:bg-blue-100 text-blue-700 font-bold py-3 px-4 rounded-2xl transition-all duration-300 transform hover:scale-105 border border-blue-200"
            >
              Tout ajouter au panier
            </button>
            <button className="bg-green-50 hover:bg-green-100 text-green-700 font-bold py-3 px-4 rounded-2xl transition-all duration-300 transform hover:scale-105 border border-green-200">
              Partager ma liste
            </button>
            <button
              onClick={() => {
                favorites.forEach(id => onToggleFavorite(id));
              }}
              className="bg-red-50 hover:bg-red-100 text-red-700 font-bold py-3 px-4 rounded-2xl transition-all duration-300 transform hover:scale-105 border border-red-200"
            >
              Vider les favoris
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FavoritesPage;
