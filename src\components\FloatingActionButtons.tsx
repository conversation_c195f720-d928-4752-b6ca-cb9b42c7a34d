import React, { useState } from 'react';
import { 
  Plus, 
  MessageCircle, 
  Phone, 
  Mail, 
  Share2,
  Download,
  Bookmark,
  Filter,
  SortAsc,
  Grid,
  List,
  X
} from 'lucide-react';

interface FloatingActionButtonsProps {
  onAction: (action: string) => void;
  showFilters?: boolean;
  viewMode?: 'grid' | 'list';
}

const FloatingActionButtons: React.FC<FloatingActionButtonsProps> = ({
  onAction,
  showFilters = false,
  viewMode = 'grid'
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const mainActions = [
    {
      id: 'contact',
      icon: MessageCircle,
      label: 'Contact',
      color: 'bg-blue-500 hover:bg-blue-600',
      action: () => onAction('contact')
    },
    {
      id: 'call',
      icon: Phone,
      label: 'Appeler',
      color: 'bg-green-500 hover:bg-green-600',
      action: () => onAction('call')
    },
    {
      id: 'email',
      icon: Mail,
      label: 'Email',
      color: 'bg-purple-500 hover:bg-purple-600',
      action: () => onAction('email')
    },
    {
      id: 'share',
      icon: Share2,
      label: 'Partager',
      color: 'bg-pink-500 hover:bg-pink-600',
      action: () => onAction('share')
    }
  ];

  const utilityActions = [
    {
      id: 'filter',
      icon: Filter,
      label: 'Filtrer',
      color: 'bg-gray-500 hover:bg-gray-600',
      action: () => onAction('filter'),
      show: showFilters
    },
    {
      id: 'sort',
      icon: SortAsc,
      label: 'Trier',
      color: 'bg-indigo-500 hover:bg-indigo-600',
      action: () => onAction('sort')
    },
    {
      id: 'view',
      icon: viewMode === 'grid' ? List : Grid,
      label: viewMode === 'grid' ? 'Liste' : 'Grille',
      color: 'bg-teal-500 hover:bg-teal-600',
      action: () => onAction('toggleView')
    },
    {
      id: 'bookmark',
      icon: Bookmark,
      label: 'Sauvegarder',
      color: 'bg-yellow-500 hover:bg-yellow-600',
      action: () => onAction('bookmark')
    },
    {
      id: 'download',
      icon: Download,
      label: 'Télécharger',
      color: 'bg-cyan-500 hover:bg-cyan-600',
      action: () => onAction('download')
    }
  ];

  const visibleUtilityActions = utilityActions.filter(action => action.show !== false);

  return (
    <div className="fixed bottom-24 right-4 z-40">
      {/* Actions secondaires */}
      {isExpanded && (
        <div className="mb-4 space-y-3">
          {/* Actions principales */}
          {mainActions.map((action, index) => (
            <div
              key={action.id}
              className="flex items-center space-x-3 animate-fade-in-up"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <span className="bg-black/80 text-white text-xs px-2 py-1 rounded-lg whitespace-nowrap">
                {action.label}
              </span>
              <button
                onClick={action.action}
                className={`w-12 h-12 ${action.color} text-white rounded-full shadow-lg flex items-center justify-center transition-all duration-300 hover:scale-110 touch-optimized`}
              >
                <action.icon className="w-5 h-5" />
              </button>
            </div>
          ))}

          {/* Séparateur */}
          <div className="w-full h-px bg-gray-300 my-2" />

          {/* Actions utilitaires */}
          {visibleUtilityActions.map((action, index) => (
            <div
              key={action.id}
              className="flex items-center space-x-3 animate-fade-in-up"
              style={{ animationDelay: `${(mainActions.length + index) * 0.1}s` }}
            >
              <span className="bg-black/80 text-white text-xs px-2 py-1 rounded-lg whitespace-nowrap">
                {action.label}
              </span>
              <button
                onClick={action.action}
                className={`w-10 h-10 ${action.color} text-white rounded-full shadow-lg flex items-center justify-center transition-all duration-300 hover:scale-110 touch-optimized`}
              >
                <action.icon className="w-4 h-4" />
              </button>
            </div>
          ))}
        </div>
      )}

      {/* Bouton principal */}
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className={`w-14 h-14 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white rounded-full shadow-2xl flex items-center justify-center transition-all duration-300 hover:scale-110 touch-optimized ${
          isExpanded ? 'rotate-45' : 'rotate-0'
        }`}
        style={{
          boxShadow: '0 8px 25px rgba(249, 115, 22, 0.4)'
        }}
      >
        {isExpanded ? <X className="w-6 h-6" /> : <Plus className="w-6 h-6" />}
      </button>

      {/* Indicateur d'actions disponibles */}
      {!isExpanded && (
        <div className="absolute -top-2 -left-2 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center animate-pulse">
          <span className="text-white text-xs font-bold">
            {mainActions.length + visibleUtilityActions.length}
          </span>
        </div>
      )}
    </div>
  );
};

export default FloatingActionButtons;
