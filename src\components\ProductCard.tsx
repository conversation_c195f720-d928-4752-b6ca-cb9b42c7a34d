import React from 'react';
import { Product } from '../types';
import { ShoppingCart, Calendar, Package, Palette, Ruler, Eye } from 'lucide-react';

interface ProductCardProps {
  product: Product;
  onProductClick: (product: Product) => void;
  onBuy: (product: Product) => void;
  onReserve: (product: Product) => void;
}

const ProductCard: React.FC<ProductCardProps> = ({ product, onProductClick, onBuy, onReserve }) => {
  const getCategoryName = (category: string) => {
    switch (category) {
      case 'doors': return 'Portes';
      case 'windows': return 'Fenêtres';
      case 'aluminum': return 'Aluminium';
      case 'carpentry': return 'Menuiserie';
      default: return category;
    }
  };

  return (
    <div className="bg-white rounded-3xl shadow-xl overflow-hidden hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 group border-2 border-orange-100">
      <div className="relative overflow-hidden cursor-pointer" onClick={() => onProductClick(product)}>
        <img
          src={product.image}
          alt={product.name}
          className="w-full h-64 object-cover transition-transform duration-700 group-hover:scale-110"
        />
        <div className="absolute top-4 left-4 px-3 py-1 rounded-full text-xs font-bold text-white bg-gradient-to-r from-gray-800 to-black">
          {getCategoryName(product.category)}
        </div>
        <div className="absolute top-4 right-4 bg-white/95 backdrop-blur-sm rounded-full px-3 py-1 border border-orange-200">
          <span className="text-orange-600 font-bold text-lg">{product.price}€</span>
        </div>
        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-300 flex items-center justify-center">
          <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-white/95 rounded-full p-3 shadow-lg">
            <Eye className="w-6 h-6 text-orange-600" />
          </div>
        </div>
      </div>

      <div className="p-6">
        <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-orange-600 transition-colors duration-300">
          {product.name}
        </h3>
        
        <p className="text-gray-600 mb-4 text-sm leading-relaxed line-clamp-2">
          {product.description}
        </p>

        <div className="grid grid-cols-2 gap-4 mb-6">
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
              <Ruler className="w-4 h-4 text-orange-600" />
            </div>
            <span className="font-medium">{product.size}</span>
          </div>
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
              <Palette className="w-4 h-4 text-orange-600" />
            </div>
            <span className="font-medium">{product.color}</span>
          </div>
          <div className="flex items-center space-x-2 text-sm text-gray-600 col-span-2">
            <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
              <Package className="w-4 h-4 text-orange-600" />
            </div>
            <span className={`font-semibold ${product.stock > 10 ? 'text-green-600' : product.stock > 0 ? 'text-orange-600' : 'text-red-600'}`}>
              {product.stock > 0 ? `${product.stock} en stock` : 'Rupture de stock'}
            </span>
          </div>
        </div>

        <div className="flex space-x-3">
          <button
            onClick={(e) => {
              e.stopPropagation();
              onBuy(product);
            }}
            disabled={product.stock === 0}
            className="flex-1 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white py-3 px-4 rounded-2xl font-bold transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center space-x-2 shadow-lg"
          >
            <ShoppingCart className="w-4 h-4" />
            <span>Acheter</span>
          </button>
          
          <button
            onClick={(e) => {
              e.stopPropagation();
              onReserve(product);
            }}
            disabled={product.stock === 0}
            className="flex-1 bg-gradient-to-r from-gray-800 to-black hover:from-gray-900 hover:to-gray-800 text-white py-3 px-4 rounded-2xl font-bold transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center space-x-2 shadow-lg"
          >
            <Calendar className="w-4 h-4" />
            <span>Réserver</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default ProductCard;