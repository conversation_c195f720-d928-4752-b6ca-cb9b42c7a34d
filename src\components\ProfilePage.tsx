import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { 
  User, 
  Phone, 
  Mail, 
  MapPin, 
  Edit, 
  Save, 
  X, 
  Camera,
  Shield,
  Bell,
  CreditCard,
  Settings,
  LogOut,
  ArrowLeft
} from 'lucide-react';

interface ProfilePageProps {
  onBack: () => void;
}

const ProfilePage: React.FC<ProfilePageProps> = ({ onBack }) => {
  const { user, logout } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [profileData, setProfileData] = useState({
    name: user?.name || 'Client Premium',
    phone: '+212 661234567',
    email: '<EMAIL>',
    address: 'Casablanca, Maroc',
    avatar: ''
  });

  const handleSave = () => {
    setIsEditing(false);
    // Ici vous pouvez sauvegarder les données
  };

  const stats = [
    { label: 'Commandes', value: '12', color: 'text-blue-600' },
    { label: 'Favoris', value: '8', color: 'text-red-600' },
    { label: 'Points', value: '2,450', color: 'text-green-600' },
    { label: 'Économies', value: '1,200 DH', color: 'text-orange-600' }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-white via-orange-50 to-orange-100">
      {/* Header */}
      <div className="bg-white/80 backdrop-blur-xl border-b border-orange-200 sticky top-0 z-40">
        <div className="px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <button
                onClick={onBack}
                className="p-2 hover:bg-gray-100 rounded-full transition-colors duration-200"
              >
                <ArrowLeft className="w-6 h-6 text-gray-700" />
              </button>
              <h1 className="text-xl font-bold text-gray-900">Mon Profil</h1>
            </div>
            <button
              onClick={() => setIsEditing(!isEditing)}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors duration-200"
            >
              {isEditing ? <X className="w-6 h-6 text-gray-700" /> : <Edit className="w-6 h-6 text-gray-700" />}
            </button>
          </div>
        </div>
      </div>

      <div className="p-4 space-y-6">
        {/* Profile Card */}
        <div className="bg-white rounded-3xl p-6 shadow-xl border border-orange-100">
          <div className="flex items-center space-x-4 mb-6">
            <div className="relative">
              <div className="w-20 h-20 bg-gradient-to-r from-orange-500 to-orange-600 rounded-full flex items-center justify-center">
                <User className="w-10 h-10 text-white" />
              </div>
              {isEditing && (
                <button className="absolute -bottom-2 -right-2 w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center shadow-lg">
                  <Camera className="w-4 h-4 text-white" />
                </button>
              )}
            </div>
            <div className="flex-1">
              {isEditing ? (
                <input
                  type="text"
                  value={profileData.name}
                  onChange={(e) => setProfileData({...profileData, name: e.target.value})}
                  className="text-xl font-bold text-gray-900 bg-gray-50 rounded-lg px-3 py-2 w-full"
                />
              ) : (
                <h2 className="text-xl font-bold text-gray-900">{profileData.name}</h2>
              )}
              <p className="text-gray-600">Client Premium</p>
            </div>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {stats.map((stat, index) => (
              <div key={index} className="text-center p-3 bg-gray-50 rounded-2xl">
                <p className={`text-2xl font-bold ${stat.color}`}>{stat.value}</p>
                <p className="text-sm text-gray-600">{stat.label}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Contact Info */}
        <div className="bg-white rounded-3xl p-6 shadow-xl border border-orange-100">
          <h3 className="text-lg font-bold text-gray-900 mb-4">Informations de contact</h3>
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <Phone className="w-5 h-5 text-orange-500" />
              {isEditing ? (
                <input
                  type="tel"
                  value={profileData.phone}
                  onChange={(e) => setProfileData({...profileData, phone: e.target.value})}
                  className="flex-1 bg-gray-50 rounded-lg px-3 py-2 text-gray-900"
                />
              ) : (
                <span className="text-gray-900">{profileData.phone}</span>
              )}
            </div>
            <div className="flex items-center space-x-3">
              <Mail className="w-5 h-5 text-orange-500" />
              {isEditing ? (
                <input
                  type="email"
                  value={profileData.email}
                  onChange={(e) => setProfileData({...profileData, email: e.target.value})}
                  className="flex-1 bg-gray-50 rounded-lg px-3 py-2 text-gray-900"
                />
              ) : (
                <span className="text-gray-900">{profileData.email}</span>
              )}
            </div>
            <div className="flex items-center space-x-3">
              <MapPin className="w-5 h-5 text-orange-500" />
              {isEditing ? (
                <input
                  type="text"
                  value={profileData.address}
                  onChange={(e) => setProfileData({...profileData, address: e.target.value})}
                  className="flex-1 bg-gray-50 rounded-lg px-3 py-2 text-gray-900"
                />
              ) : (
                <span className="text-gray-900">{profileData.address}</span>
              )}
            </div>
          </div>
          
          {isEditing && (
            <button
              onClick={handleSave}
              className="w-full mt-4 bg-gradient-to-r from-orange-500 to-orange-600 text-white font-bold py-3 px-4 rounded-2xl transition-all duration-300 transform hover:scale-105 flex items-center justify-center space-x-2"
            >
              <Save className="w-4 h-4" />
              <span>Sauvegarder</span>
            </button>
          )}
        </div>

        {/* Settings */}
        <div className="bg-white rounded-3xl p-6 shadow-xl border border-orange-100">
          <h3 className="text-lg font-bold text-gray-900 mb-4">Paramètres</h3>
          <div className="space-y-3">
            <button className="w-full flex items-center space-x-3 p-4 hover:bg-gray-50 rounded-2xl transition-colors duration-200">
              <Bell className="w-5 h-5 text-gray-600" />
              <span className="text-gray-900 font-medium">Notifications</span>
              <div className="ml-auto w-12 h-6 bg-orange-500 rounded-full relative">
                <div className="w-5 h-5 bg-white rounded-full absolute right-0.5 top-0.5"></div>
              </div>
            </button>
            
            <button className="w-full flex items-center space-x-3 p-4 hover:bg-gray-50 rounded-2xl transition-colors duration-200">
              <Shield className="w-5 h-5 text-gray-600" />
              <span className="text-gray-900 font-medium">Sécurité</span>
            </button>
            
            <button className="w-full flex items-center space-x-3 p-4 hover:bg-gray-50 rounded-2xl transition-colors duration-200">
              <CreditCard className="w-5 h-5 text-gray-600" />
              <span className="text-gray-900 font-medium">Moyens de paiement</span>
            </button>
            
            <button className="w-full flex items-center space-x-3 p-4 hover:bg-gray-50 rounded-2xl transition-colors duration-200">
              <Settings className="w-5 h-5 text-gray-600" />
              <span className="text-gray-900 font-medium">Préférences</span>
            </button>
          </div>
        </div>

        {/* Logout */}
        <div className="bg-white rounded-3xl p-6 shadow-xl border border-orange-100">
          <button
            onClick={logout}
            className="w-full flex items-center justify-center space-x-3 p-4 bg-red-50 hover:bg-red-100 rounded-2xl transition-colors duration-200 border border-red-200"
          >
            <LogOut className="w-5 h-5 text-red-600" />
            <span className="text-red-600 font-medium">Se déconnecter</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default ProfilePage;
