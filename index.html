<!doctype html>
<html lang="fr">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/278426439_103701565654071_785914601533428886_n copy.jpg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover" />

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json" />

    <!-- Meta tags pour PWA -->
    <meta name="theme-color" content="#f97316" />
    <meta name="background-color" content="#ffffff" />
    <meta name="display" content="standalone" />
    <meta name="orientation" content="portrait-primary" />

    <!-- Meta tags pour mobile -->
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="Doorly" />

    <!-- Icons pour iOS -->
    <link rel="apple-touch-icon" href="/278426439_103701565654071_785914601533428886_n copy.jpg" />
    <link rel="apple-touch-icon" sizes="152x152" href="/icon-152x152.png" />
    <link rel="apple-touch-icon" sizes="180x180" href="/icon-180x180.png" />

    <!-- Meta tags SEO et réseaux sociaux -->
    <title>Doorly - Portes & Fenêtres Premium</title>
    <meta name="description" content="Application mobile Doorly - Spécialiste en portes et fenêtres aluminium et menuiserie. Découvrez notre gamme premium, commandez et réservez en ligne." />
    <meta name="keywords" content="portes, fenêtres, aluminium, menuiserie, Doorly, mobile, app, commande, réservation" />
    <meta name="author" content="Doorly" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://doorly.app/" />
    <meta property="og:title" content="Doorly - Portes & Fenêtres Premium" />
    <meta property="og:description" content="Application mobile pour la vente de portes et fenêtres premium" />
    <meta property="og:image" content="/278426439_103701565654071_785914601533428886_n copy.jpg" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://doorly.app/" />
    <meta property="twitter:title" content="Doorly - Portes & Fenêtres Premium" />
    <meta property="twitter:description" content="Application mobile pour la vente de portes et fenêtres premium" />
    <meta property="twitter:image" content="/278426439_103701565654071_785914601533428886_n copy.jpg" />

    <!-- Preload des polices -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />

    <!-- CSS pour le splash screen -->
    <style>
      #splash-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #ffffff 0%, #fff7ed 50%, #ffedd5 100%);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        transition: opacity 0.5s ease-out;
      }

      #splash-screen.hidden {
        opacity: 0;
        pointer-events: none;
      }

      .splash-logo {
        width: 120px;
        height: 120px;
        background: linear-gradient(135deg, #f97316, #ea580c);
        border-radius: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 30px;
        animation: pulse 2s infinite;
        box-shadow: 0 20px 40px rgba(249, 115, 22, 0.3);
      }

      .splash-title {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        font-size: 32px;
        font-weight: 800;
        color: #1f2937;
        margin-bottom: 10px;
        background: linear-gradient(135deg, #f97316, #ea580c);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .splash-subtitle {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        font-size: 16px;
        color: #6b7280;
        margin-bottom: 40px;
      }

      .splash-loader {
        width: 40px;
        height: 40px;
        border: 4px solid #fed7aa;
        border-top: 4px solid #f97316;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  </head>
  <body>
    <!-- Splash Screen -->
    <div id="splash-screen">
      <div class="splash-logo">
        <svg width="60" height="60" fill="white" viewBox="0 0 24 24">
          <path d="M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z"/>
        </svg>
      </div>
      <h1 class="splash-title">Doorly</h1>
      <p class="splash-subtitle">Portes & Fenêtres Premium</p>
      <div class="splash-loader"></div>
    </div>

    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>

    <!-- Service Worker Registration -->
    <script>
      // Masquer le splash screen après le chargement
      window.addEventListener('load', () => {
        setTimeout(() => {
          const splash = document.getElementById('splash-screen');
          if (splash) {
            splash.classList.add('hidden');
            setTimeout(() => splash.remove(), 500);
          }
        }, 1500);
      });

      // Enregistrer le Service Worker
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
          navigator.serviceWorker.register('/sw.js')
            .then((registration) => {
              console.log('🎉 Service Worker enregistré avec succès:', registration.scope);

              // Vérifier les mises à jour
              registration.addEventListener('updatefound', () => {
                const newWorker = registration.installing;
                if (newWorker) {
                  newWorker.addEventListener('statechange', () => {
                    if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                      // Nouvelle version disponible
                      if (confirm('Une nouvelle version de Doorly est disponible. Voulez-vous la charger ?')) {
                        newWorker.postMessage({ type: 'SKIP_WAITING' });
                        window.location.reload();
                      }
                    }
                  });
                }
              });
            })
            .catch((error) => {
              console.log('❌ Échec de l\'enregistrement du Service Worker:', error);
            });
        });
      }

      // Gestion de l'installation PWA
      let deferredPrompt;
      window.addEventListener('beforeinstallprompt', (e) => {
        e.preventDefault();
        deferredPrompt = e;

        // Afficher un bouton d'installation personnalisé
        const installButton = document.createElement('button');
        installButton.textContent = '📱 Installer l\'app';
        installButton.style.cssText = `
          position: fixed;
          bottom: 20px;
          right: 20px;
          background: linear-gradient(135deg, #f97316, #ea580c);
          color: white;
          border: none;
          padding: 12px 20px;
          border-radius: 25px;
          font-weight: 600;
          box-shadow: 0 4px 15px rgba(249, 115, 22, 0.3);
          cursor: pointer;
          z-index: 1000;
          font-size: 14px;
          transition: all 0.3s ease;
        `;

        installButton.addEventListener('mouseenter', () => {
          installButton.style.transform = 'translateY(-2px)';
          installButton.style.boxShadow = '0 6px 20px rgba(249, 115, 22, 0.4)';
        });

        installButton.addEventListener('mouseleave', () => {
          installButton.style.transform = 'translateY(0)';
          installButton.style.boxShadow = '0 4px 15px rgba(249, 115, 22, 0.3)';
        });

        installButton.addEventListener('click', () => {
          deferredPrompt.prompt();
          deferredPrompt.userChoice.then((choiceResult) => {
            if (choiceResult.outcome === 'accepted') {
              console.log('🎉 L\'utilisateur a accepté l\'installation');
              installButton.remove();
            }
            deferredPrompt = null;
          });
        });

        document.body.appendChild(installButton);

        // Masquer le bouton après 10 secondes
        setTimeout(() => {
          if (installButton.parentNode) {
            installButton.style.opacity = '0';
            setTimeout(() => installButton.remove(), 300);
          }
        }, 10000);
      });

      // Gestion de l'installation réussie
      window.addEventListener('appinstalled', () => {
        console.log('🎉 PWA installée avec succès');
        deferredPrompt = null;
      });
    </script>
  </body>
</html>