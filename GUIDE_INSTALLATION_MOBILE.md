# 📱 Guide d'installation Doorly sur smartphone

## 🚀 Méthodes d'installation

### Option 1 : Progressive Web App (PWA) - **Recommandée**

Votre application Doorly est maintenant une PWA complète ! Voici comment l'installer :

#### 📱 **Sur Android (Chrome/Edge/Samsung Internet)**

1. **Ouvrez votre navigateur** (Chrome, Edge, ou Samsung Internet)
2. **Allez sur votre site** : `http://localhost:5173` (en développement)
3. **Attendez le popup d'installation** qui apparaîtra automatiquement
4. **Cliquez sur "Installer l'application"**
5. **Confirmez l'installation**

**OU manuellement :**
1. Ouvrez le menu du navigateur (⋮)
2. Sélectionnez "Ajouter à l'écran d'accueil" ou "Installer l'app"
3. Confirmez l'installation

#### 🍎 **Sur iPhone/iPad (Safari)**

1. **Ouvrez Safari**
2. **Allez sur votre site** : `http://localhost:5173`
3. **Appuyez sur le bouton de partage** (⬆️) en bas de l'écran
4. **Faites défiler et sélectionnez "Sur l'écran d'accueil"**
5. **Personnalisez le nom** (Doorly) et **appuyez sur "Ajouter"**

### Option 2 : Expo/React Native (Pour une vraie app native)

Si vous voulez une vraie application native dans les stores :

#### Installation d'Expo CLI
```bash
npm install -g @expo/cli
```

#### Créer un nouveau projet Expo
```bash
npx create-expo-app DoorlyMobile --template blank-typescript
cd DoorlyMobile
```

#### Installer les dépendances nécessaires
```bash
npx expo install expo-router expo-constants expo-linking expo-status-bar
npm install @react-navigation/native @react-navigation/stack
npm install react-native-screens react-native-safe-area-context
```

### Option 3 : Capacitor (Hybride)

Pour transformer votre app React en app native :

#### Installation de Capacitor
```bash
npm install @capacitor/core @capacitor/cli
npm install @capacitor/android @capacitor/ios
```

#### Configuration
```bash
npx cap init DoorlyMobile com.doorly.app
npm run build
npx cap add android
npx cap add ios
```

## 🛠️ Configuration pour le développement mobile

### 1. **Tester sur votre réseau local**

Pour tester sur votre smartphone pendant le développement :

```bash
# Démarrer le serveur avec accès réseau
npm run dev -- --host

# Ou avec Vite
npx vite --host
```

Votre app sera accessible sur : `http://[VOTRE-IP]:5173`

### 2. **Trouver votre adresse IP**

**Windows :**
```cmd
ipconfig
```

**Mac/Linux :**
```bash
ifconfig
```

Cherchez votre adresse IP locale (ex: *************)

### 3. **Accéder depuis votre smartphone**

1. **Connectez votre smartphone au même WiFi** que votre ordinateur
2. **Ouvrez le navigateur** sur votre smartphone
3. **Tapez l'adresse** : `http://*************:5173` (remplacez par votre IP)

## 🎯 Fonctionnalités PWA activées

Votre application Doorly inclut maintenant :

### ✅ **Fonctionnalités natives**
- 📱 Installation sur l'écran d'accueil
- 🔄 Fonctionne hors-ligne
- 📬 Notifications push
- 🎨 Splash screen personnalisé
- 🔄 Synchronisation en arrière-plan
- 📊 Cache intelligent
- 🔒 HTTPS requis en production

### ✅ **Optimisations mobile**
- 👆 Navigation tactile optimisée
- 📱 Interface responsive
- ⚡ Chargement ultra-rapide
- 🎨 Thème adaptatif
- 🔄 Pull-to-refresh
- 📳 Vibrations tactiles

## 🚀 Déploiement en production

### 1. **Hébergement recommandé**

**Netlify (Gratuit) :**
```bash
npm run build
# Glissez-déposez le dossier dist/ sur netlify.com
```

**Vercel (Gratuit) :**
```bash
npm install -g vercel
vercel
```

**Firebase Hosting :**
```bash
npm install -g firebase-tools
firebase init hosting
npm run build
firebase deploy
```

### 2. **Configuration HTTPS**

⚠️ **Important** : Les PWA nécessitent HTTPS en production !

Tous les hébergeurs recommandés fournissent HTTPS automatiquement.

### 3. **Domaine personnalisé**

Une fois déployé, vous pouvez utiliser votre propre domaine :
- `https://doorly.app`
- `https://app.doorly.com`

## 📱 Test sur différents appareils

### **Outils de test**

1. **Chrome DevTools** (F12)
   - Mode responsive
   - Simulation d'appareils
   - Test PWA

2. **BrowserStack** (payant)
   - Test sur vrais appareils
   - Différents navigateurs

3. **Lighthouse** (gratuit)
   - Audit PWA
   - Performance mobile
   - Accessibilité

### **Commande de test PWA**
```bash
npx lighthouse http://localhost:5173 --view
```

## 🔧 Dépannage

### **Problèmes courants**

1. **L'app ne s'installe pas**
   - Vérifiez que vous êtes en HTTPS (en production)
   - Vérifiez le manifest.json
   - Vérifiez le service worker

2. **Pas de notifications**
   - Demandez la permission notifications
   - Vérifiez le service worker
   - Testez en HTTPS

3. **Cache problématique**
   - Videz le cache du navigateur
   - Désinstallez et réinstallez l'app
   - Vérifiez la version du service worker

### **Commandes de debug**
```bash
# Vérifier le service worker
console.log('SW registered:', navigator.serviceWorker.controller);

# Vérifier le manifest
console.log('Manifest:', window.navigator.manifest);

# Vérifier l'installation
console.log('Standalone:', window.matchMedia('(display-mode: standalone)').matches);
```

## 🎉 Félicitations !

Votre application Doorly est maintenant prête pour mobile ! 

### **Prochaines étapes :**

1. ✅ **Testez sur votre smartphone**
2. ✅ **Partagez avec vos utilisateurs**
3. ✅ **Déployez en production**
4. ✅ **Soumettez aux app stores** (optionnel avec PWA)

### **Ressources utiles :**

- [PWA Builder](https://www.pwabuilder.com/) - Outils PWA
- [Web.dev PWA](https://web.dev/progressive-web-apps/) - Guide complet
- [Can I Use](https://caniuse.com/) - Compatibilité navigateurs

---

**🚀 Votre app Doorly est maintenant une vraie application mobile !**
