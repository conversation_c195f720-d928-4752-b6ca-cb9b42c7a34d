import React, { useState, useEffect, useRef } from 'react';
import {
  ChevronUp,
  ArrowRight,
  Sparkles,
  Zap,
  Shield,
  Star,
  Play,
  Volume2,
  VolumeX,
  Layers,
  Cpu,
  Wifi,
  Battery,
  Signal,
  Globe,
  Lock,
  Smartphone,
  Tablet,
  Monitor,
  Headphones,
  Camera,
  Mic,
  Settings,
  User,
  Heart,
  ShoppingBag,
  CreditCard,
  Gift,
  Award,
  TrendingUp,
  BarChart3,
  PieChart,
  Activity
} from 'lucide-react';

interface WelcomePageProps {
  onEnter: () => void;
}

const WelcomePage: React.FC<WelcomePageProps> = ({ onEnter }) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [showSwipeHint, setShowSwipeHint] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [currentScene, setCurrentScene] = useState(0);
  const [soundEnabled, setSoundEnabled] = useState(false);
  const [time, setTime] = useState(new Date());
  const [batteryLevel, setBatteryLevel] = useState(85);
  const [isInteracting, setIsInteracting] = useState(false);
  const [particleCount, setParticleCount] = useState(100);
  const containerRef = useRef<HTMLDivElement>(null);
  const audioRef = useRef<HTMLAudioElement>(null);

  const scenes = [
    {
      title: "DOORLY",
      subtitle: "L'Excellence Redéfinie",
      description: "Découvrez l'avenir des portes et fenêtres avec notre technologie révolutionnaire",
      color: "from-orange-400 via-orange-500 to-orange-600",
      bgColor: "from-gray-900 via-gray-800 to-black",
      accentColor: "orange-500",
      particles: "orange"
    },
    {
      title: "INNOVATION",
      subtitle: "Technologie 5D",
      description: "Expérience immersive avec intelligence artificielle et réalité augmentée",
      color: "from-cyan-400 via-blue-500 to-purple-600",
      bgColor: "from-blue-900 via-purple-900 to-black",
      accentColor: "cyan-400",
      particles: "cyan"
    },
    {
      title: "PREMIUM",
      subtitle: "Luxe Absolu",
      description: "Matériaux premium, design exclusif et service personnalisé de classe mondiale",
      color: "from-purple-400 via-pink-500 to-red-600",
      bgColor: "from-purple-900 via-pink-900 to-black",
      accentColor: "purple-400",
      particles: "purple"
    },
    {
      title: "FUTUR",
      subtitle: "Vision 2030",
      description: "Anticipez l'avenir avec nos solutions intelligentes et connectées",
      color: "from-green-400 via-emerald-500 to-teal-600",
      bgColor: "from-emerald-900 via-teal-900 to-black",
      accentColor: "emerald-400",
      particles: "emerald"
    }
  ];

  useEffect(() => {
    // Animation d'entrée progressive avec effets sonores
    const loadSequence = async () => {
      await new Promise(resolve => setTimeout(resolve, 500));
      setIsLoaded(true);

      await new Promise(resolve => setTimeout(resolve, 2000));
      setShowSwipeHint(true);

      // Augmenter progressivement les particules
      setParticleCount(150);
    };

    loadSequence();

    // Changement automatique de scène avec transition fluide
    const sceneInterval = setInterval(() => {
      setCurrentScene((prev) => (prev + 1) % scenes.length);
    }, 6000);

    // Horloge en temps réel
    const timeInterval = setInterval(() => {
      setTime(new Date());
    }, 1000);

    // Simulation batterie
    const batteryInterval = setInterval(() => {
      setBatteryLevel(prev => {
        const newLevel = prev + (Math.random() - 0.5) * 2;
        return Math.max(20, Math.min(100, newLevel));
      });
    }, 5000);

    return () => {
      clearInterval(sceneInterval);
      clearInterval(timeInterval);
      clearInterval(batteryInterval);
    };
  }, [scenes.length]);

  // Gestion du mouvement de la souris pour les effets 3D
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        const x = ((e.clientX - rect.left) / rect.width) * 100;
        const y = ((e.clientY - rect.top) / rect.height) * 100;
        setMousePosition({ x, y });
      }
    };

    const handleDeviceOrientation = (e: DeviceOrientationEvent) => {
      if (e.gamma !== null && e.beta !== null) {
        const x = ((e.gamma + 90) / 180) * 100;
        const y = ((e.beta + 90) / 180) * 100;
        setMousePosition({ x: Math.max(0, Math.min(100, x)), y: Math.max(0, Math.min(100, y)) });
      }
    };

    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('deviceorientation', handleDeviceOrientation);

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('deviceorientation', handleDeviceOrientation);
    };
  }, []);

  const handleSwipeUp = (e: React.TouchEvent) => {
    const touch = e.touches[0];
    const startY = touch.clientY;

    const handleTouchMove = (moveEvent: TouchEvent) => {
      const currentY = moveEvent.touches[0].clientY;
      const deltaY = startY - currentY;

      if (deltaY > 100) {
        onEnter();
        document.removeEventListener('touchmove', handleTouchMove);
      }
    };

    document.addEventListener('touchmove', handleTouchMove);
    document.addEventListener('touchend', () => {
      document.removeEventListener('touchmove', handleTouchMove);
    }, { once: true });
  };

  return (
    <div
      ref={containerRef}
      className={`min-h-screen bg-gradient-to-br ${scenes[currentScene].bgColor} flex items-center justify-center relative overflow-hidden transition-all duration-2000`}
      onTouchStart={handleSwipeUp}
      onClick={onEnter}
      onMouseMove={() => setIsInteracting(true)}
      onMouseLeave={() => setIsInteracting(false)}
    >
      {/* Barre de statut futuriste */}
      <div className="absolute top-0 left-0 right-0 z-50 bg-black/20 backdrop-blur-xl border-b border-white/10">
        <div className="flex items-center justify-between px-6 py-3">
          <div className="flex items-center space-x-4">
            <div className="text-white/90 text-sm font-mono">
              {time.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })}
            </div>
            <div className="flex items-center space-x-1">
              <Signal className="w-4 h-4 text-green-400" />
              <Wifi className="w-4 h-4 text-blue-400" />
            </div>
          </div>

          <div className="text-white/90 text-lg font-bold tracking-wider">
            DOORLY OS
          </div>

          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-1">
              <Battery className="w-4 h-4 text-green-400" />
              <span className="text-white/90 text-sm">{Math.round(batteryLevel)}%</span>
            </div>
            <button
              onClick={() => setSoundEnabled(!soundEnabled)}
              className="p-1 rounded-full bg-white/10 hover:bg-white/20 transition-colors"
            >
              {soundEnabled ? <Volume2 className="w-4 h-4 text-white" /> : <VolumeX className="w-4 h-4 text-white/60" />}
            </button>
          </div>
        </div>
      </div>
      {/* Arrière-plan 5D révolutionnaire */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Matrice 5D avec profondeur infinie */}
        <div
          className="absolute inset-0 opacity-30"
          style={{
            background: `
              radial-gradient(ellipse at ${mousePosition.x}% ${mousePosition.y}%,
                rgba(${scenes[currentScene].particles === 'orange' ? '249,115,22' :
                      scenes[currentScene].particles === 'cyan' ? '6,182,212' :
                      scenes[currentScene].particles === 'purple' ? '168,85,247' : '16,185,129'},0.4) 0%,
                transparent 70%),
              conic-gradient(from ${mousePosition.x * 3.6}deg at 50% 50%,
                transparent 0deg,
                rgba(${scenes[currentScene].particles === 'orange' ? '249,115,22' :
                      scenes[currentScene].particles === 'cyan' ? '6,182,212' :
                      scenes[currentScene].particles === 'purple' ? '168,85,247' : '16,185,129'},0.1) 90deg,
                transparent 180deg,
                rgba(${scenes[currentScene].particles === 'orange' ? '249,115,22' :
                      scenes[currentScene].particles === 'cyan' ? '6,182,212' :
                      scenes[currentScene].particles === 'purple' ? '168,85,247' : '16,185,129'},0.1) 270deg,
                transparent 360deg),
              repeating-linear-gradient(
                ${mousePosition.x}deg,
                transparent 0px,
                rgba(255,255,255,0.03) 1px,
                transparent 2px,
                transparent 40px
              ),
              repeating-linear-gradient(
                ${90 + mousePosition.y}deg,
                transparent 0px,
                rgba(255,255,255,0.03) 1px,
                transparent 2px,
                transparent 40px
              )
            `,
            transform: `perspective(2000px) rotateX(${mousePosition.y * 0.2 - 10}deg) rotateY(${mousePosition.x * 0.2 - 10}deg) translateZ(${isInteracting ? 100 : 0}px)`,
            transition: 'transform 0.3s ease-out'
          }}
        />

        {/* Tunnels de lumière 5D */}
        {[...Array(8)].map((_, i) => (
          <div
            key={i}
            className="absolute inset-0 opacity-20"
            style={{
              background: `conic-gradient(from ${i * 45 + mousePosition.x}deg at 50% 50%,
                transparent 0deg,
                rgba(${scenes[currentScene].particles === 'orange' ? '249,115,22' :
                      scenes[currentScene].particles === 'cyan' ? '6,182,212' :
                      scenes[currentScene].particles === 'purple' ? '168,85,247' : '16,185,129'},0.2) ${45 + i * 10}deg,
                transparent ${90 + i * 10}deg)`,
              transform: `perspective(1500px) rotateZ(${i * 45 + Date.now() * 0.01}deg) scale(${1 + i * 0.1})`,
              animation: `spin-slow ${20 + i * 5}s linear infinite`
            }}
          />
        ))}

        {/* Particules 5D intelligentes */}
        {[...Array(particleCount)].map((_, i) => {
          const delay = Math.random() * 8;
          const duration = 4 + Math.random() * 6;
          const size = 1 + Math.random() * 6;
          const depth = Math.random() * 300;
          const speed = 0.5 + Math.random() * 2;

          return (
            <div
              key={i}
              className="absolute animate-float-5d"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                width: `${size}px`,
                height: `${size}px`,
                background: `radial-gradient(circle,
                  rgba(${scenes[currentScene].particles === 'orange' ? '249,115,22' :
                        scenes[currentScene].particles === 'cyan' ? '6,182,212' :
                        scenes[currentScene].particles === 'purple' ? '168,85,247' : '16,185,129'},0.8) 0%,
                  rgba(${scenes[currentScene].particles === 'orange' ? '249,115,22' :
                        scenes[currentScene].particles === 'cyan' ? '6,182,212' :
                        scenes[currentScene].particles === 'purple' ? '168,85,247' : '16,185,129'},0.3) 50%,
                  transparent 100%)`,
                borderRadius: '50%',
                boxShadow: `0 0 ${size * 2}px rgba(${scenes[currentScene].particles === 'orange' ? '249,115,22' :
                                                    scenes[currentScene].particles === 'cyan' ? '6,182,212' :
                                                    scenes[currentScene].particles === 'purple' ? '168,85,247' : '16,185,129'},0.6)`,
                animationDelay: `${delay}s`,
                animationDuration: `${duration}s`,
                transform: `
                  translateZ(${depth}px)
                  translateX(${(mousePosition.x - 50) * speed}px)
                  translateY(${(mousePosition.y - 50) * speed}px)
                  rotateX(${mousePosition.y * 0.5}deg)
                  rotateY(${mousePosition.x * 0.5}deg)
                  scale(${1 + Math.sin((Date.now() * 0.001 + i) * speed) * 0.3})
                `,
                opacity: isInteracting ? 0.8 : 0.4,
                transition: 'opacity 0.3s ease-out, transform 0.1s ease-out'
              }}
            />
          );
        })}

        {/* Vagues d'énergie 5D */}
        {[...Array(6)].map((_, i) => (
          <div
            key={i}
            className="absolute inset-0 rounded-full border opacity-20 animate-pulse-wave"
            style={{
              borderColor: `rgba(${scenes[currentScene].particles === 'orange' ? '249,115,22' :
                                  scenes[currentScene].particles === 'cyan' ? '6,182,212' :
                                  scenes[currentScene].particles === 'purple' ? '168,85,247' : '16,185,129'},0.3)`,
              borderWidth: '2px',
              transform: `scale(${0.5 + i * 0.3}) translateZ(${i * 50}px)`,
              animationDelay: `${i * 0.5}s`,
              animationDuration: `${3 + i}s`
            }}
          />
        ))}

        {/* Champs d'énergie quantique */}
        <div
          className="absolute inset-0 opacity-25"
          style={{
            background: `
              radial-gradient(ellipse at ${mousePosition.x}% ${mousePosition.y}%,
                rgba(${scenes[currentScene].particles === 'orange' ? '249,115,22' :
                      scenes[currentScene].particles === 'cyan' ? '6,182,212' :
                      scenes[currentScene].particles === 'purple' ? '168,85,247' : '16,185,129'},0.3) 0%,
                transparent 50%),
              conic-gradient(from ${mousePosition.x * 2}deg at ${mousePosition.x}% ${mousePosition.y}%,
                transparent 0deg,
                rgba(255,255,255,0.1) 30deg,
                transparent 60deg,
                rgba(${scenes[currentScene].particles === 'orange' ? '249,115,22' :
                      scenes[currentScene].particles === 'cyan' ? '6,182,212' :
                      scenes[currentScene].particles === 'purple' ? '168,85,247' : '16,185,129'},0.2) 120deg,
                transparent 180deg)
            `,
            transform: `perspective(1500px) rotateX(${mousePosition.y * 0.1}deg) rotateY(${mousePosition.x * 0.1}deg) rotateZ(${Date.now() * 0.005}deg)`,
            filter: 'blur(1px)'
          }}
        />

        {/* Distorsions spatio-temporelles */}
        {[...Array(4)].map((_, i) => (
          <div
            key={i}
            className="absolute inset-0 opacity-15"
            style={{
              background: `conic-gradient(from ${i * 90 + mousePosition.x * 2}deg at 50% 50%,
                transparent 0deg,
                rgba(255,255,255,0.1) ${45 + i * 20}deg,
                transparent ${90 + i * 20}deg,
                rgba(${scenes[currentScene].particles === 'orange' ? '249,115,22' :
                      scenes[currentScene].particles === 'cyan' ? '6,182,212' :
                      scenes[currentScene].particles === 'purple' ? '168,85,247' : '16,185,129'},0.2) ${180 + i * 20}deg,
                transparent ${270 + i * 20}deg)`,
              transform: `perspective(2000px) rotateZ(${i * 90 + Date.now() * 0.002}deg) scale(${1.5 + i * 0.2}) translateZ(${i * 100}px)`,
              filter: `blur(${2 + i}px)`
            }}
          />
        ))}
      </div>

      {/* Interface de contrôle quantique */}
      <div className="absolute top-20 right-6 z-50 space-y-4">
        {/* Panneau de contrôle principal */}
        <div className="bg-black/30 backdrop-blur-2xl border border-white/20 rounded-2xl p-4 space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-white/80 text-xs font-mono">QUANTUM CTRL</span>
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
          </div>

          {/* Sélecteur de scène */}
          <div className="grid grid-cols-2 gap-2">
            {scenes.map((scene, i) => (
              <button
                key={i}
                onClick={() => setCurrentScene(i)}
                className={`p-2 rounded-lg text-xs font-semibold transition-all duration-300 ${
                  i === currentScene
                    ? `bg-gradient-to-r ${scene.color} text-white shadow-lg scale-105`
                    : 'bg-white/10 text-white/70 hover:bg-white/20'
                }`}
              >
                {scene.title.slice(0, 3)}
              </button>
            ))}
          </div>

          {/* Contrôles audio */}
          <button
            onClick={() => setSoundEnabled(!soundEnabled)}
            className="w-full flex items-center justify-center space-x-2 p-2 bg-white/10 hover:bg-white/20 rounded-lg transition-all duration-300"
          >
            {soundEnabled ? <Volume2 className="w-4 h-4 text-white" /> : <VolumeX className="w-4 h-4 text-white/60" />}
            <span className="text-white/80 text-xs">AUDIO</span>
          </button>

          {/* Contrôle particules */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-white/70 text-xs">PARTICLES</span>
              <span className="text-white/90 text-xs font-mono">{particleCount}</span>
            </div>
            <input
              type="range"
              min="50"
              max="200"
              value={particleCount}
              onChange={(e) => setParticleCount(Number(e.target.value))}
              className="w-full h-1 bg-white/20 rounded-lg appearance-none cursor-pointer"
            />
          </div>
        </div>

        {/* Indicateurs système */}
        <div className="bg-black/30 backdrop-blur-2xl border border-white/20 rounded-2xl p-3 space-y-2">
          <div className="flex items-center space-x-2">
            <Cpu className="w-4 h-4 text-cyan-400" />
            <div className="flex-1 bg-white/20 rounded-full h-1">
              <div className="bg-cyan-400 h-1 rounded-full w-3/4 animate-pulse" />
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Activity className="w-4 h-4 text-green-400" />
            <div className="flex-1 bg-white/20 rounded-full h-1">
              <div className="bg-green-400 h-1 rounded-full w-5/6 animate-pulse" />
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Layers className="w-4 h-4 text-purple-400" />
            <div className="flex-1 bg-white/20 rounded-full h-1">
              <div className="bg-purple-400 h-1 rounded-full w-4/5 animate-pulse" />
            </div>
          </div>
        </div>
      </div>

      {/* Contenu principal holographique 5D */}
      <div className="text-center z-10 px-8 relative">
        {/* Logo holographique révolutionnaire */}
        <div className={`relative mb-16 transform transition-all duration-2000 ${isLoaded ? 'scale-100 opacity-100 translate-y-0' : 'scale-30 opacity-0 translate-y-32'}`}>

          {/* Matrice holographique 5D */}
          <div className="relative mx-auto w-80 h-80 perspective-2000 transform-3d">
            {/* Anneaux quantiques */}
            {[...Array(8)].map((_, i) => (
              <div
                key={i}
                className="absolute inset-0 rounded-full border-2 opacity-40 animate-spin-slow"
                style={{
                  borderColor: `rgba(${scenes[currentScene].particles === 'orange' ? '249,115,22' :
                                     scenes[currentScene].particles === 'cyan' ? '6,182,212' :
                                     scenes[currentScene].particles === 'purple' ? '168,85,247' : '16,185,129'},0.6)`,
                  borderStyle: i % 2 === 0 ? 'solid' : 'dashed',
                  transform: `
                    rotateX(${i * 22.5}deg)
                    rotateY(${mousePosition.x * 0.3 + i * 15}deg)
                    rotateZ(${mousePosition.y * 0.2 + i * 10}deg)
                    scale(${1 - i * 0.08})
                    translateZ(${i * 20}px)
                  `,
                  animationDelay: `${i * 0.3}s`,
                  animationDuration: `${12 + i * 3}s`,
                  boxShadow: `0 0 ${20 + i * 5}px rgba(${scenes[currentScene].particles === 'orange' ? '249,115,22' :
                                                          scenes[currentScene].particles === 'cyan' ? '6,182,212' :
                                                          scenes[currentScene].particles === 'purple' ? '168,85,247' : '16,185,129'},0.3)`
                }}
              />
            ))}

            {/* Noyau holographique central */}
            <div
              className="absolute inset-0 flex items-center justify-center"
              style={{
                transform: `
                  rotateY(${mousePosition.x * 0.5}deg)
                  rotateX(${mousePosition.y * 0.3}deg)
                  translateZ(${isInteracting ? 100 : 50}px)
                  scale(${isInteracting ? 1.1 : 1})
                `,
                transition: 'transform 0.3s ease-out'
              }}
            >
              {/* Logo DOORLY holographique 5D */}
              <div className="relative group">
                {/* Structure principale du logo */}
                <div
                  className={`w-32 h-40 bg-gradient-to-br ${scenes[currentScene].color} rounded-r-3xl shadow-2xl transform transition-all duration-700 group-hover:scale-110`}
                  style={{
                    transform: `rotateY(20deg) translateZ(40px)`,
                    boxShadow: `
                      0 30px 60px rgba(${scenes[currentScene].particles === 'orange' ? '249,115,22' :
                                        scenes[currentScene].particles === 'cyan' ? '6,182,212' :
                                        scenes[currentScene].particles === 'purple' ? '168,85,247' : '16,185,129'},0.4),
                      inset 0 0 30px rgba(255,255,255,0.2)
                    `
                  }}
                >
                  {/* Texture holographique avancée */}
                  <div className="absolute inset-0 bg-gradient-to-b from-white/40 via-transparent to-white/20 rounded-r-3xl" />
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent rounded-r-3xl animate-shimmer" />

                  {/* Lignes de circuit quantique */}
                  {[...Array(6)].map((_, i) => (
                    <div
                      key={i}
                      className="absolute bg-white/30 animate-pulse"
                      style={{
                        left: `${10 + i * 12}%`,
                        top: `${20 + i * 10}%`,
                        width: '2px',
                        height: `${15 + i * 5}%`,
                        animationDelay: `${i * 0.2}s`,
                        borderRadius: '1px'
                      }}
                    />
                  ))}

                  {/* Poignée quantique */}
                  <div
                    className="absolute top-1/2 right-3 w-4 h-4 bg-gradient-to-br from-yellow-300 to-yellow-500 rounded-full transform -translate-y-1/2 shadow-lg animate-pulse"
                    style={{
                      boxShadow: '0 0 15px rgba(255,255,0,0.6)'
                    }}
                  />

                  {/* Scanner holographique */}
                  <div className="absolute inset-0 overflow-hidden rounded-r-3xl">
                    <div
                      className="absolute w-full h-1 bg-gradient-to-r from-transparent via-white/70 to-transparent animate-scan"
                      style={{
                        boxShadow: '0 0 10px rgba(255,255,255,0.8)'
                      }}
                    />
                  </div>

                  {/* Particules d'énergie */}
                  {[...Array(12)].map((_, i) => (
                    <div
                      key={i}
                      className="absolute w-1 h-1 bg-white rounded-full animate-float-particle"
                      style={{
                        left: `${Math.random() * 80 + 10}%`,
                        top: `${Math.random() * 80 + 10}%`,
                        animationDelay: `${i * 0.3}s`,
                        animationDuration: `${2 + Math.random() * 3}s`,
                        boxShadow: '0 0 4px rgba(255,255,255,0.8)'
                      }}
                    />
                  ))}
                </div>

                {/* Côté 5D avec profondeur */}
                <div
                  className={`absolute top-0 right-0 w-6 h-40 bg-gradient-to-b ${scenes[currentScene].color.split(' ')[3]} ${scenes[currentScene].color.split(' ')[5]} transform skew-y-12 origin-top shadow-xl`}
                  style={{
                    transform: 'skewY(15deg) translateZ(20px)',
                    boxShadow: `inset 0 0 20px rgba(0,0,0,0.3)`
                  }}
                />

                {/* Reflets dynamiques multiples */}
                <div className="absolute top-4 left-4 w-8 h-16 bg-white/50 rounded-full blur-sm animate-pulse" />
                <div className="absolute bottom-6 right-2 w-4 h-8 bg-white/30 rounded-full blur-sm animate-pulse" style={{ animationDelay: '0.5s' }} />

                {/* Aura énergétique */}
                <div
                  className="absolute inset-0 rounded-r-3xl opacity-60 animate-pulse-glow"
                  style={{
                    background: `radial-gradient(ellipse at center,
                      rgba(${scenes[currentScene].particles === 'orange' ? '249,115,22' :
                            scenes[currentScene].particles === 'cyan' ? '6,182,212' :
                            scenes[currentScene].particles === 'purple' ? '168,85,247' : '16,185,129'},0.3) 0%,
                      transparent 70%)`,
                    transform: 'scale(1.2)',
                    filter: 'blur(8px)'
                  }}
                />
              </div>
            </div>

            {/* Satellites quantiques orbitaux */}
            {[...Array(16)].map((_, i) => (
              <div
                key={i}
                className="absolute w-3 h-3 rounded-full animate-orbit"
                style={{
                  background: `radial-gradient(circle,
                    rgba(${scenes[currentScene].particles === 'orange' ? '249,115,22' :
                          scenes[currentScene].particles === 'cyan' ? '6,182,212' :
                          scenes[currentScene].particles === 'purple' ? '168,85,247' : '16,185,129'},0.9) 0%,
                    rgba(255,255,255,0.6) 50%,
                    transparent 100%)`,
                  left: '50%',
                  top: '50%',
                  transformOrigin: `${80 + i * 15}px 0`,
                  transform: `rotate(${i * 22.5}deg) translateX(${80 + i * 15}px) translateZ(${i * 5}px)`,
                  animationDelay: `${i * 0.2}s`,
                  animationDuration: `${6 + i * 0.8}s`,
                  boxShadow: `0 0 10px rgba(${scenes[currentScene].particles === 'orange' ? '249,115,22' :
                                              scenes[currentScene].particles === 'cyan' ? '6,182,212' :
                                              scenes[currentScene].particles === 'purple' ? '168,85,247' : '16,185,129'},0.8)`
                }}
              />
            ))}
          </div>

          {/* Titre principal holographique 5D */}
          <div className={`relative mb-12 transform transition-all duration-2000 ${isLoaded ? 'scale-100 opacity-100' : 'scale-50 opacity-0'}`} style={{ animationDelay: '1s' }}>
            <div className="relative">
              {/* Titre principal avec effet holographique avancé */}
              <h1
                className={`text-7xl md:text-9xl font-black bg-gradient-to-r ${scenes[currentScene].color} bg-clip-text text-transparent tracking-wider mb-6 animate-hologram-flicker`}
                style={{
                  transform: `perspective(2000px) rotateY(${mousePosition.x * 0.15 - 7.5}deg) rotateX(${mousePosition.y * 0.08 - 4}deg) translateZ(${isInteracting ? 50 : 0}px)`,
                  textShadow: `
                    0 0 30px rgba(${scenes[currentScene].particles === 'orange' ? '249,115,22' :
                                   scenes[currentScene].particles === 'cyan' ? '6,182,212' :
                                   scenes[currentScene].particles === 'purple' ? '168,85,247' : '16,185,129'},0.8),
                    0 0 60px rgba(${scenes[currentScene].particles === 'orange' ? '249,115,22' :
                                   scenes[currentScene].particles === 'cyan' ? '6,182,212' :
                                   scenes[currentScene].particles === 'purple' ? '168,85,247' : '16,185,129'},0.4)
                  `,
                  filter: 'drop-shadow(0 0 20px currentColor)'
                }}
              >
                {scenes[currentScene].title.split('').map((letter, i) => (
                  <span
                    key={i}
                    className="inline-block animate-letter-float"
                    style={{
                      animationDelay: `${i * 0.15}s`,
                      transform: `
                        translateY(${Math.sin((Date.now() * 0.002 + i) * 3) * 8}px)
                        translateZ(${Math.cos((Date.now() * 0.001 + i) * 2) * 10}px)
                        rotateY(${Math.sin((Date.now() * 0.001 + i) * 1.5) * 5}deg)
                      `
                    }}
                  >
                    {letter}
                  </span>
                ))}
              </h1>

              {/* Sous-titre avec effet quantique */}
              <div className="relative overflow-hidden mb-4">
                <p
                  className={`text-2xl md:text-3xl font-bold text-white/90 animate-typewriter`}
                  style={{
                    animationDelay: '1.5s',
                    textShadow: `0 0 20px rgba(${scenes[currentScene].particles === 'orange' ? '249,115,22' :
                                                  scenes[currentScene].particles === 'cyan' ? '6,182,212' :
                                                  scenes[currentScene].particles === 'purple' ? '168,85,247' : '16,185,129'},0.6)`
                  }}
                >
                  {scenes[currentScene].subtitle}
                </p>
                <div className="absolute right-0 top-0 w-1 h-full bg-white animate-blink" />
              </div>

              {/* Description avec effet de révélation */}
              <p
                className={`text-lg md:text-xl text-white/80 max-w-2xl mx-auto leading-relaxed transform transition-all duration-1000 ${isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}
                style={{
                  animationDelay: '2s',
                  textShadow: '0 0 10px rgba(255,255,255,0.3)'
                }}
              >
                {scenes[currentScene].description}
              </p>

              {/* Effets de profondeur holographique */}
              <div className="absolute inset-0 -z-10">
                {[...Array(5)].map((_, i) => (
                  <h1
                    key={i}
                    className={`text-7xl md:text-9xl font-black text-white/5 tracking-wider`}
                    style={{
                      transform: `
                        translate(${(i + 1) * 3}px, ${(i + 1) * 3}px)
                        scale(${1 + i * 0.03})
                        rotateY(${i * 2}deg)
                        translateZ(${-i * 20}px)
                      `
                    }}
                  >
                    {scenes[currentScene].title}
                  </h1>
                ))}
              </div>
            </div>
          </div>

          {/* Indicateurs de fonctionnalités quantiques */}
          <div className={`grid grid-cols-4 gap-6 mb-16 transform transition-all duration-1500 ${isLoaded ? 'scale-100 opacity-100 translate-y-0' : 'scale-50 opacity-0 translate-y-16'}`} style={{ animationDelay: '2.5s' }}>
            {[
              { icon: Shield, label: 'Sécurité Quantique', color: 'text-green-400', description: '256-bit' },
              { icon: Zap, label: 'Vitesse 5G', color: 'text-yellow-400', description: 'Ultra-rapide' },
              { icon: Sparkles, label: 'IA Avancée', color: 'text-purple-400', description: 'Smart Tech' },
              { icon: Globe, label: 'Connecté', color: 'text-blue-400', description: 'IoT Ready' }
            ].map((feature, i) => (
              <div
                key={i}
                className="relative group cursor-pointer"
                style={{
                  transform: `translateZ(${i * 15}px) rotateY(${(mousePosition.x - 50) * 0.15}deg) rotateX(${(mousePosition.y - 50) * 0.1}deg)`
                }}
                onClick={() => setIsInteracting(!isInteracting)}
              >
                <div className="bg-black/30 backdrop-blur-2xl border border-white/20 rounded-3xl p-6 text-center transition-all duration-500 group-hover:bg-black/50 group-hover:scale-110 group-hover:border-white/40">
                  <div className="relative mb-4">
                    <feature.icon className={`w-12 h-12 ${feature.color} mx-auto animate-pulse`} />
                    <div className={`absolute inset-0 w-12 h-12 ${feature.color} mx-auto opacity-30 animate-ping`} />
                  </div>
                  <h3 className="text-white/90 text-sm font-bold mb-1">{feature.label}</h3>
                  <p className="text-white/60 text-xs">{feature.description}</p>
                </div>

                {/* Halo énergétique */}
                <div
                  className={`absolute inset-0 rounded-3xl opacity-0 group-hover:opacity-60 transition-all duration-500 blur-xl`}
                  style={{
                    background: `radial-gradient(circle,
                      rgba(${scenes[currentScene].particles === 'orange' ? '249,115,22' :
                            scenes[currentScene].particles === 'cyan' ? '6,182,212' :
                            scenes[currentScene].particles === 'purple' ? '168,85,247' : '16,185,129'},0.4) 0%,
                      transparent 70%)`
                  }}
                />

                {/* Particules orbitales */}
                {[...Array(4)].map((_, j) => (
                  <div
                    key={j}
                    className="absolute w-1 h-1 bg-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                    style={{
                      left: '50%',
                      top: '50%',
                      transform: `rotate(${j * 90}deg) translateX(40px)`,
                      animationDelay: `${j * 0.1}s`
                    }}
                  />
                ))}
              </div>
            ))}
          </div>
        </div>

        {/* Interface de navigation quantique */}
        <div className={`fixed bottom-12 left-1/2 transform -translate-x-1/2 transition-all duration-1500 ${showSwipeHint ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-16'}`}>
          <div className="relative">
            {/* Panneau principal */}
            <div className="bg-black/40 backdrop-blur-2xl border border-white/30 rounded-3xl px-10 py-6 flex items-center space-x-6">
              {/* Indicateur de glissement holographique */}
              <div className="flex flex-col items-center space-y-3">
                <div className="relative">
                  {[...Array(3)].map((_, i) => (
                    <ChevronUp
                      key={i}
                      className="absolute inset-0 w-8 h-8 text-white animate-bounce"
                      style={{
                        animationDelay: `${i * 0.3}s`,
                        opacity: 1 - i * 0.3,
                        transform: `translateY(${i * 4}px)`
                      }}
                    />
                  ))}
                </div>
                <div className="flex space-x-2">
                  {[...Array(5)].map((_, i) => (
                    <div
                      key={i}
                      className={`w-2 h-2 rounded-full animate-pulse`}
                      style={{
                        animationDelay: `${i * 0.2}s`,
                        background: `rgba(${scenes[currentScene].particles === 'orange' ? '249,115,22' :
                                           scenes[currentScene].particles === 'cyan' ? '6,182,212' :
                                           scenes[currentScene].particles === 'purple' ? '168,85,247' : '16,185,129'},0.8)`
                      }}
                    />
                  ))}
                </div>
              </div>

              {/* Texte avec effet quantique */}
              <div className="text-center space-y-2">
                <p className="text-white/95 text-lg font-bold animate-hologram-flicker">
                  ACCÈS QUANTIQUE
                </p>
                <p className="text-white/70 text-sm">
                  Glissez ↑ ou touchez pour entrer
                </p>
                <div className="flex items-center justify-center space-x-2 text-xs text-white/50">
                  <Smartphone className="w-3 h-3" />
                  <span>Mobile Optimisé</span>
                  <Tablet className="w-3 h-3" />
                </div>
              </div>

              {/* Bouton d'activation principal */}
              <button
                onClick={onEnter}
                className="relative w-16 h-16 bg-gradient-to-r from-white/20 to-white/30 hover:from-white/30 hover:to-white/40 rounded-full flex items-center justify-center transition-all duration-500 group overflow-hidden"
                style={{
                  boxShadow: `0 0 30px rgba(${scenes[currentScene].particles === 'orange' ? '249,115,22' :
                                              scenes[currentScene].particles === 'cyan' ? '6,182,212' :
                                              scenes[currentScene].particles === 'purple' ? '168,85,247' : '16,185,129'},0.6)`
                }}
              >
                <Play className="w-6 h-6 text-white group-hover:scale-125 transition-transform duration-300 relative z-10" />

                {/* Effet de scan */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-shimmer" />

                {/* Particules d'activation */}
                {[...Array(8)].map((_, i) => (
                  <div
                    key={i}
                    className="absolute w-1 h-1 bg-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                    style={{
                      left: '50%',
                      top: '50%',
                      transform: `rotate(${i * 45}deg) translateX(25px)`,
                      animationDelay: `${i * 0.1}s`
                    }}
                  />
                ))}
              </button>
            </div>

            {/* Ondes de pulsation multiples */}
            {[...Array(3)].map((_, i) => (
              <div
                key={i}
                className="absolute inset-0 rounded-3xl border-2 opacity-30 animate-ping"
                style={{
                  borderColor: `rgba(${scenes[currentScene].particles === 'orange' ? '249,115,22' :
                                     scenes[currentScene].particles === 'cyan' ? '6,182,212' :
                                     scenes[currentScene].particles === 'purple' ? '168,85,247' : '16,185,129'},0.6)`,
                  animationDelay: `${i * 0.5}s`,
                  animationDuration: `${2 + i}s`
                }}
              />
            ))}
          </div>
        </div>

        {/* Statistiques holographiques flottantes */}
        <div className="absolute bottom-32 left-8 right-8 grid grid-cols-4 gap-4 opacity-80">
          {[
            { icon: Users, value: '10K+', label: 'Clients Satisfaits', color: 'text-green-400' },
            { icon: Award, value: '99.9%', label: 'Satisfaction', color: 'text-yellow-400' },
            { icon: TrendingUp, value: '24/7', label: 'Support Premium', color: 'text-blue-400' },
            { icon: Shield, value: '100%', label: 'Sécurisé', color: 'text-purple-400' }
          ].map((stat, i) => (
            <div key={i} className="text-center">
              <div className="bg-black/30 backdrop-blur-xl border border-white/20 rounded-2xl p-4 mb-2 hover:scale-105 transition-transform duration-300">
                <stat.icon className={`w-6 h-6 ${stat.color} mx-auto mb-2 animate-pulse`} />
                <div className="text-xl font-bold text-white">{stat.value}</div>
              </div>
              <div className="text-xs text-white/70">{stat.label}</div>
            </div>
          ))}
        </div>

        {/* Indicateur de progression de chargement */}
        <div className="absolute top-24 left-1/2 transform -translate-x-1/2">
          <div className="bg-black/30 backdrop-blur-xl border border-white/20 rounded-full px-6 py-2 flex items-center space-x-3">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
            <span className="text-white/80 text-sm font-mono">SYSTÈME QUANTIQUE ACTIF</span>
            <div className="flex space-x-1">
              {[...Array(3)].map((_, i) => (
                <div
                  key={i}
                  className="w-1 h-4 bg-white/40 rounded-full animate-pulse"
                  style={{ animationDelay: `${i * 0.2}s` }}
                />
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Overlay de profondeur quantique */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-black/10 pointer-events-none" />

      {/* Effets de scan holographique multiples */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute w-full h-1 bg-gradient-to-r from-transparent via-white/60 to-transparent animate-scan-horizontal" />
        <div
          className="absolute h-full w-1 bg-gradient-to-b from-transparent via-white/40 to-transparent animate-scan-vertical"
          style={{ left: `${mousePosition.x}%` }}
        />
      </div>

      {/* Particules de transition */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-white rounded-full opacity-30 animate-float-particle"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${2 + Math.random() * 4}s`
            }}
          />
        ))}
      </div>

      {/* Audio de fond (optionnel) */}
      <audio ref={audioRef} loop>
        <source src="/sounds/ambient-tech.mp3" type="audio/mpeg" />
      </audio>
    </div>
  );
};

export default WelcomePage;
