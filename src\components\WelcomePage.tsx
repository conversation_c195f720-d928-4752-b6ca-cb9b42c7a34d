import React, { useState, useEffect, useRef } from 'react';
import {
  ChevronUp,
  ArrowRight,
  Sparkles,
  Zap,
  Shield,
  Star,
  Play,
  Volume2,
  VolumeX
} from 'lucide-react';

interface WelcomePageProps {
  onEnter: () => void;
}

const WelcomePage: React.FC<WelcomePageProps> = ({ onEnter }) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [showSwipeHint, setShowSwipeHint] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [currentScene, setCurrentScene] = useState(0);
  const [soundEnabled, setSoundEnabled] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  const scenes = [
    {
      title: "DOORLY",
      subtitle: "L'Excellence Redéfinie",
      color: "from-orange-400 via-orange-500 to-orange-600",
      bgColor: "from-orange-50 via-white to-orange-100"
    },
    {
      title: "INNOVATION",
      subtitle: "Technologie Avancée",
      color: "from-blue-400 via-blue-500 to-blue-600",
      bgColor: "from-blue-50 via-white to-blue-100"
    },
    {
      title: "PREMIUM",
      subtitle: "Qualité Supérieure",
      color: "from-purple-400 via-purple-500 to-purple-600",
      bgColor: "from-purple-50 via-white to-purple-100"
    }
  ];

  useEffect(() => {
    // Animation d'entrée progressive
    setTimeout(() => setIsLoaded(true), 800);
    setTimeout(() => setShowSwipeHint(true), 4000);

    // Changement automatique de scène
    const sceneInterval = setInterval(() => {
      setCurrentScene((prev) => (prev + 1) % scenes.length);
    }, 5000);

    return () => clearInterval(sceneInterval);
  }, [scenes.length]);

  // Gestion du mouvement de la souris pour les effets 3D
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        const x = ((e.clientX - rect.left) / rect.width) * 100;
        const y = ((e.clientY - rect.top) / rect.height) * 100;
        setMousePosition({ x, y });
      }
    };

    const handleDeviceOrientation = (e: DeviceOrientationEvent) => {
      if (e.gamma !== null && e.beta !== null) {
        const x = ((e.gamma + 90) / 180) * 100;
        const y = ((e.beta + 90) / 180) * 100;
        setMousePosition({ x: Math.max(0, Math.min(100, x)), y: Math.max(0, Math.min(100, y)) });
      }
    };

    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('deviceorientation', handleDeviceOrientation);

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('deviceorientation', handleDeviceOrientation);
    };
  }, []);

  const handleSwipeUp = (e: React.TouchEvent) => {
    const touch = e.touches[0];
    const startY = touch.clientY;

    const handleTouchMove = (moveEvent: TouchEvent) => {
      const currentY = moveEvent.touches[0].clientY;
      const deltaY = startY - currentY;

      if (deltaY > 100) {
        onEnter();
        document.removeEventListener('touchmove', handleTouchMove);
      }
    };

    document.addEventListener('touchmove', handleTouchMove);
    document.addEventListener('touchend', () => {
      document.removeEventListener('touchmove', handleTouchMove);
    }, { once: true });
  };

  return (
    <div
      ref={containerRef}
      className={`min-h-screen bg-gradient-to-br ${scenes[currentScene].bgColor} flex items-center justify-center relative overflow-hidden transition-all duration-1000`}
      onTouchStart={handleSwipeUp}
      onClick={onEnter}
    >
      {/* Arrière-plan 3D dynamique */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Grille 3D animée */}
        <div
          className="absolute inset-0 opacity-20"
          style={{
            background: `
              radial-gradient(circle at ${mousePosition.x}% ${mousePosition.y}%, rgba(249,115,22,0.3) 0%, transparent 50%),
              linear-gradient(45deg, transparent 24%, rgba(249,115,22,0.1) 25%, rgba(249,115,22,0.1) 26%, transparent 27%, transparent 74%, rgba(249,115,22,0.1) 75%, rgba(249,115,22,0.1) 76%, transparent 77%),
              linear-gradient(-45deg, transparent 24%, rgba(249,115,22,0.1) 25%, rgba(249,115,22,0.1) 26%, transparent 27%, transparent 74%, rgba(249,115,22,0.1) 75%, rgba(249,115,22,0.1) 76%, transparent 77%)
            `,
            backgroundSize: '60px 60px, 60px 60px, 60px 60px',
            transform: `perspective(1000px) rotateX(${mousePosition.y * 0.1 - 5}deg) rotateY(${mousePosition.x * 0.1 - 5}deg) translateZ(0)`
          }}
        />

        {/* Particules 3D flottantes */}
        {[...Array(50)].map((_, i) => {
          const delay = Math.random() * 5;
          const duration = 3 + Math.random() * 4;
          const size = 2 + Math.random() * 4;
          return (
            <div
              key={i}
              className="absolute rounded-full opacity-40 animate-float-3d"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                width: `${size}px`,
                height: `${size}px`,
                background: `linear-gradient(45deg, ${scenes[currentScene].color.split(' ')[1]}, ${scenes[currentScene].color.split(' ')[3]})`,
                animationDelay: `${delay}s`,
                animationDuration: `${duration}s`,
                transform: `translateZ(${Math.random() * 100}px) translateX(${(mousePosition.x - 50) * 0.1}px) translateY(${(mousePosition.y - 50) * 0.1}px)`
              }}
            />
          );
        })}

        {/* Orbes lumineux 3D */}
        {[...Array(8)].map((_, i) => (
          <div
            key={i}
            className="absolute rounded-full blur-xl animate-pulse-glow"
            style={{
              left: `${20 + (i * 15)}%`,
              top: `${10 + Math.sin(i) * 30}%`,
              width: `${80 + Math.random() * 120}px`,
              height: `${80 + Math.random() * 120}px`,
              background: `radial-gradient(circle, ${scenes[currentScene].color.split(' ')[1]}40, transparent)`,
              animationDelay: `${i * 0.5}s`,
              transform: `translateZ(${i * 20}px) scale(${1 + Math.sin((mousePosition.x + mousePosition.y + i * 30) * 0.01) * 0.2})`
            }}
          />
        ))}

        {/* Rayons de lumière dynamiques */}
        <div
          className="absolute inset-0 opacity-30"
          style={{
            background: `conic-gradient(from ${mousePosition.x * 3.6}deg at ${mousePosition.x}% ${mousePosition.y}%, transparent 0deg, ${scenes[currentScene].color.split(' ')[1]}20 45deg, transparent 90deg, ${scenes[currentScene].color.split(' ')[3]}20 135deg, transparent 180deg, ${scenes[currentScene].color.split(' ')[1]}20 225deg, transparent 270deg, ${scenes[currentScene].color.split(' ')[3]}20 315deg, transparent 360deg)`,
            transform: `rotate(${Date.now() * 0.01}deg)`
          }}
        />
      </div>

      {/* Interface de contrôle futuriste */}
      <div className="absolute top-6 right-6 z-50 flex space-x-3">
        <button
          onClick={() => setSoundEnabled(!soundEnabled)}
          className="w-12 h-12 bg-white/10 backdrop-blur-xl border border-white/20 rounded-full flex items-center justify-center text-white hover:bg-white/20 transition-all duration-300"
        >
          {soundEnabled ? <Volume2 className="w-5 h-5" /> : <VolumeX className="w-5 h-5" />}
        </button>
        <div className="flex space-x-1">
          {scenes.map((_, i) => (
            <button
              key={i}
              onClick={() => setCurrentScene(i)}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${
                i === currentScene
                  ? `bg-gradient-to-r ${scenes[currentScene].color} scale-125`
                  : 'bg-white/30 hover:bg-white/50'
              }`}
            />
          ))}
        </div>
      </div>

      {/* Contenu principal ultra-moderne */}
      <div className="text-center z-10 px-8 relative">
        {/* Logo 3D holographique */}
        <div className={`relative mb-12 transform transition-all duration-1500 ${isLoaded ? 'scale-100 opacity-100 translate-y-0' : 'scale-50 opacity-0 translate-y-16'}`}>

          {/* Hologramme de base */}
          <div className="relative mx-auto w-48 h-48 perspective-1000">
            {/* Cercles holographiques */}
            {[...Array(5)].map((_, i) => (
              <div
                key={i}
                className="absolute inset-0 rounded-full border-2 opacity-30 animate-spin-slow"
                style={{
                  borderColor: scenes[currentScene].color.split(' ')[1],
                  transform: `rotateX(${i * 20}deg) rotateY(${mousePosition.x * 0.2}deg) scale(${1 - i * 0.1})`,
                  animationDelay: `${i * 0.2}s`,
                  animationDuration: `${8 + i * 2}s`
                }}
              />
            ))}

            {/* Logo central 3D */}
            <div
              className="absolute inset-0 flex items-center justify-center"
              style={{
                transform: `rotateY(${mousePosition.x * 0.3}deg) rotateX(${mousePosition.y * 0.2}deg) translateZ(50px)`
              }}
            >
              {/* Porte 3D futuriste */}
              <div className="relative group">
                <div
                  className={`w-20 h-28 bg-gradient-to-br ${scenes[currentScene].color} rounded-r-2xl shadow-2xl transform transition-all duration-500 group-hover:scale-110`}
                  style={{
                    transform: `rotateY(15deg) translateZ(20px)`,
                    boxShadow: `0 20px 40px ${scenes[currentScene].color.split(' ')[1]}40`
                  }}
                >
                  {/* Texture holographique */}
                  <div className="absolute inset-0 bg-gradient-to-b from-white/30 via-transparent to-white/10 rounded-r-2xl" />
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent rounded-r-2xl animate-shimmer" />

                  {/* Poignée futuriste */}
                  <div className="absolute top-1/2 right-2 w-3 h-3 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-full transform -translate-y-1/2 shadow-lg animate-pulse" />

                  {/* Lignes de scan */}
                  <div className="absolute inset-0 overflow-hidden rounded-r-2xl">
                    <div className="absolute w-full h-0.5 bg-white/50 animate-scan" />
                  </div>
                </div>

                {/* Côté 3D */}
                <div
                  className={`absolute top-0 right-0 w-4 h-28 bg-gradient-to-b ${scenes[currentScene].color.split(' ')[3]} ${scenes[currentScene].color.split(' ')[5]} transform skew-y-12 origin-top shadow-xl`}
                  style={{ transform: 'skewY(12deg) translateZ(10px)' }}
                />

                {/* Reflets dynamiques */}
                <div className="absolute top-2 left-2 w-6 h-12 bg-white/40 rounded-full blur-sm animate-pulse" />
              </div>
            </div>

            {/* Particules orbitales */}
            {[...Array(12)].map((_, i) => (
              <div
                key={i}
                className="absolute w-2 h-2 rounded-full animate-orbit"
                style={{
                  background: `linear-gradient(45deg, ${scenes[currentScene].color.split(' ')[1]}, ${scenes[currentScene].color.split(' ')[3]})`,
                  left: '50%',
                  top: '50%',
                  transformOrigin: `${60 + i * 10}px 0`,
                  transform: `rotate(${i * 30}deg) translateX(${60 + i * 10}px)`,
                  animationDelay: `${i * 0.1}s`,
                  animationDuration: `${4 + i * 0.5}s`
                }}
              />
            ))}
          </div>

          {/* Titre principal holographique */}
          <div className={`relative mb-8 transform transition-all duration-1000 ${isLoaded ? 'scale-100 opacity-100' : 'scale-75 opacity-0'}`} style={{ animationDelay: '0.5s' }}>
            <div className="relative">
              {/* Titre principal avec effet holographique */}
              <h1
                className={`text-6xl md:text-8xl font-black bg-gradient-to-r ${scenes[currentScene].color} bg-clip-text text-transparent tracking-wider mb-4 animate-text-glow`}
                style={{
                  transform: `perspective(1000px) rotateY(${mousePosition.x * 0.1 - 5}deg) rotateX(${mousePosition.y * 0.05 - 2.5}deg)`,
                  textShadow: `0 0 20px ${scenes[currentScene].color.split(' ')[1]}50`
                }}
              >
                {scenes[currentScene].title.split('').map((letter, i) => (
                  <span
                    key={i}
                    className="inline-block animate-letter-float"
                    style={{
                      animationDelay: `${i * 0.1}s`,
                      transform: `translateY(${Math.sin((Date.now() * 0.001 + i) * 2) * 5}px)`
                    }}
                  >
                    {letter}
                  </span>
                ))}
              </h1>

              {/* Sous-titre avec effet de machine à écrire */}
              <div className="relative overflow-hidden">
                <p
                  className={`text-xl md:text-2xl font-semibold bg-gradient-to-r ${scenes[currentScene].color} bg-clip-text text-transparent opacity-80 animate-typewriter`}
                  style={{ animationDelay: '1s' }}
                >
                  {scenes[currentScene].subtitle}
                </p>
                <div className="absolute right-0 top-0 w-0.5 h-full bg-current animate-blink" />
              </div>

              {/* Effets de profondeur multiples */}
              <div className="absolute inset-0 -z-10">
                {[...Array(3)].map((_, i) => (
                  <h1
                    key={i}
                    className={`text-6xl md:text-8xl font-black text-gray-400 tracking-wider opacity-10`}
                    style={{
                      transform: `translate(${(i + 1) * 2}px, ${(i + 1) * 2}px) scale(${1 + i * 0.02})`
                    }}
                  >
                    {scenes[currentScene].title}
                  </h1>
                ))}
              </div>
            </div>
          </div>

          {/* Indicateurs de fonctionnalités futuristes */}
          <div className={`grid grid-cols-3 gap-4 mb-12 transform transition-all duration-1000 ${isLoaded ? 'scale-100 opacity-100 translate-y-0' : 'scale-75 opacity-0 translate-y-8'}`} style={{ animationDelay: '1.5s' }}>
            {[
              { icon: Shield, label: 'Sécurisé', color: 'text-green-400' },
              { icon: Zap, label: 'Rapide', color: 'text-yellow-400' },
              { icon: Sparkles, label: 'Premium', color: 'text-purple-400' }
            ].map((feature, i) => (
              <div
                key={i}
                className="relative group"
                style={{
                  transform: `translateZ(${i * 10}px) rotateY(${(mousePosition.x - 50) * 0.1}deg)`
                }}
              >
                <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-2xl p-4 text-center transition-all duration-300 group-hover:bg-white/20 group-hover:scale-110">
                  <feature.icon className={`w-8 h-8 ${feature.color} mx-auto mb-2 animate-pulse`} />
                  <span className="text-white/80 text-sm font-semibold">{feature.label}</span>
                </div>
                {/* Halo lumineux */}
                <div className={`absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-50 transition-opacity duration-300 blur-xl bg-gradient-to-r ${scenes[currentScene].color}`} />
              </div>
            ))}
          </div>
        </div>

        {/* Interface de navigation futuriste */}
        <div className={`fixed bottom-8 left-1/2 transform -translate-x-1/2 transition-all duration-1000 ${showSwipeHint ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
          <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-full px-8 py-4 flex items-center space-x-4">
            {/* Indicateur de glissement animé */}
            <div className="flex flex-col items-center space-y-2">
              <div className="relative">
                <ChevronUp className="w-6 h-6 text-white animate-bounce" />
                <div className="absolute inset-0 w-6 h-6 text-white/30 animate-bounce" style={{ animationDelay: '0.2s' }}>
                  <ChevronUp className="w-6 h-6" />
                </div>
              </div>
              <div className="flex space-x-1">
                {[...Array(3)].map((_, i) => (
                  <div
                    key={i}
                    className="w-1 h-1 bg-white/60 rounded-full animate-pulse"
                    style={{ animationDelay: `${i * 0.2}s` }}
                  />
                ))}
              </div>
            </div>

            {/* Texte avec effet holographique */}
            <div className="text-center">
              <p className="text-white/90 text-sm font-semibold animate-pulse">
                Glissez vers le haut
              </p>
              <p className="text-white/60 text-xs">
                ou touchez pour continuer
              </p>
            </div>

            {/* Bouton play alternatif */}
            <button
              onClick={onEnter}
              className="w-12 h-12 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center transition-all duration-300 group"
            >
              <Play className="w-5 h-5 text-white group-hover:scale-110 transition-transform duration-300" />
            </button>
          </div>

          {/* Onde de pulsation */}
          <div className="absolute inset-0 rounded-full border-2 border-white/20 animate-ping" />
          <div className="absolute inset-0 rounded-full border border-white/10 animate-pulse" />
        </div>

        {/* Statistiques flottantes */}
        <div className="absolute bottom-20 left-8 right-8 flex justify-between items-end opacity-60">
          <div className="text-center">
            <div className="text-2xl font-bold text-white">1000+</div>
            <div className="text-xs text-white/70">Clients</div>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center space-x-1">
              {[...Array(5)].map((_, i) => (
                <Star key={i} className="w-4 h-4 text-yellow-400 fill-current animate-pulse" style={{ animationDelay: `${i * 0.1}s` }} />
              ))}
            </div>
            <div className="text-xs text-white/70">Excellence</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-white">24/7</div>
            <div className="text-xs text-white/70">Support</div>
          </div>
        </div>
      </div>

      {/* Overlay de profondeur avec effet de verre */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/10 via-transparent to-white/5 pointer-events-none" />

      {/* Effet de scan holographique */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute w-full h-0.5 bg-gradient-to-r from-transparent via-white/50 to-transparent animate-scan-horizontal" />
      </div>
    </div>
  );
};

export default WelcomePage;
