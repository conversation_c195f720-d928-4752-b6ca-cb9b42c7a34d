import React, { useState, useEffect } from 'react';
import { 
  DoorOpen as Door, 
  ArrowRight, 
  Star, 
  Sparkles, 
  Shield, 
  Zap, 
  Award,
  Users,
  TrendingUp,
  Heart,
  Globe,
  Smartphone
} from 'lucide-react';

interface WelcomePageProps {
  onGetStarted: () => void;
}

const WelcomePage: React.FC<WelcomePageProps> = ({ onGetStarted }) => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  const slides = [
    {
      title: "Bienvenue chez Doorly",
      subtitle: "L'excellence en portes et fenêtres",
      description: "Découvrez notre gamme premium de produits avec une expérience d'achat révolutionnaire",
      color: "from-orange-500 to-orange-600"
    },
    {
      title: "Design Ultra-Moderne",
      subtitle: "Innovation et élégance",
      description: "Des produits conçus avec les dernières technologies pour votre confort et sécurité",
      color: "from-blue-500 to-blue-600"
    },
    {
      title: "Expérience Mobile",
      subtitle: "Achetez où que vous soyez",
      description: "Application mobile optimisée pour une expérience d'achat fluide et intuitive",
      color: "from-purple-500 to-purple-600"
    }
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, 4000);
    return () => clearInterval(interval);
  }, [slides.length]);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({
        x: (e.clientX / window.innerWidth) * 100,
        y: (e.clientY / window.innerHeight) * 100
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-white via-orange-50 to-orange-100 relative overflow-hidden">
      {/* Background 3D Elements */}
      <div className="absolute inset-0">
        {/* Floating geometric shapes */}
        <div 
          className="absolute w-96 h-96 bg-gradient-to-r from-orange-400/20 to-orange-600/20 rounded-full blur-3xl animate-pulse-slow"
          style={{
            left: `${20 + mousePosition.x * 0.1}%`,
            top: `${10 + mousePosition.y * 0.1}%`,
            transform: `translate(-50%, -50%) scale(${1 + mousePosition.x * 0.001})`
          }}
        />
        <div 
          className="absolute w-64 h-64 bg-gradient-to-r from-blue-400/20 to-blue-600/20 rounded-full blur-3xl animate-pulse-slow"
          style={{
            right: `${15 + mousePosition.x * 0.05}%`,
            bottom: `${20 + mousePosition.y * 0.05}%`,
            transform: `translate(50%, 50%) scale(${1 + mousePosition.y * 0.001})`,
            animationDelay: '1s'
          }}
        />
        <div 
          className="absolute w-80 h-80 bg-gradient-to-r from-purple-400/20 to-purple-600/20 rounded-full blur-3xl animate-pulse-slow"
          style={{
            left: `${60 + mousePosition.y * 0.08}%`,
            top: `${60 + mousePosition.x * 0.08}%`,
            transform: `translate(-50%, -50%) rotate(${mousePosition.x * 0.5}deg)`,
            animationDelay: '2s'
          }}
        />

        {/* 3D Grid Background */}
        <div 
          className="absolute inset-0 opacity-10"
          style={{
            backgroundImage: `
              linear-gradient(rgba(249,115,22,0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(249,115,22,0.1) 1px, transparent 1px)
            `,
            backgroundSize: '50px 50px',
            transform: `perspective(1000px) rotateX(${mousePosition.y * 0.1}deg) rotateY(${mousePosition.x * 0.1}deg)`
          }}
        />

        {/* Floating particles */}
        {Array.from({ length: 20 }, (_, i) => (
          <div
            key={i}
            className="absolute w-2 h-2 bg-orange-400/30 rounded-full animate-bounce-gentle"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${3 + Math.random() * 2}s`,
              transform: `translate(${mousePosition.x * 0.02}px, ${mousePosition.y * 0.02}px)`
            }}
          />
        ))}
      </div>

      {/* Main Content */}
      <div className="relative z-10 min-h-screen flex flex-col">
        {/* Header */}
        <header className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div 
                className="w-16 h-16 bg-gradient-to-r from-orange-500 to-orange-600 rounded-3xl flex items-center justify-center shadow-2xl shadow-orange-500/25 transform transition-transform duration-300 hover:scale-110"
                style={{
                  transform: `scale(${1 + mousePosition.x * 0.0005}) rotate(${mousePosition.x * 0.1}deg)`
                }}
              >
                <Door className="w-8 h-8 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-black text-gray-900">Doorly</h1>
                <p className="text-sm text-gray-600">Premium Experience</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              {[...Array(5)].map((_, i) => (
                <Star key={i} className="w-5 h-5 text-orange-400 fill-current animate-pulse" style={{ animationDelay: `${i * 0.2}s` }} />
              ))}
            </div>
          </div>
        </header>

        {/* Hero Section */}
        <main className="flex-1 flex items-center justify-center px-6">
          <div className="max-w-6xl mx-auto grid lg:grid-cols-2 gap-12 items-center">
            
            {/* Left Content */}
            <div className="text-center lg:text-left space-y-8">
              <div className="space-y-6">
                <div 
                  className="inline-flex items-center space-x-3 bg-white/80 backdrop-blur-xl rounded-full px-6 py-3 border border-orange-200 shadow-xl animate-fade-in-up"
                  style={{ animationDelay: '0.2s' }}
                >
                  <Sparkles className="w-5 h-5 text-orange-500 animate-pulse" />
                  <span className="text-gray-800 font-semibold">Nouvelle Expérience 2024</span>
                  <Award className="w-4 h-4 text-orange-400" />
                </div>
                
                <div 
                  className="animate-fade-in-up"
                  style={{ animationDelay: '0.4s' }}
                >
                  <h2 className="text-5xl lg:text-7xl font-black text-gray-900 mb-6 leading-tight">
                    {slides[currentSlide].title.split(' ').map((word, i) => (
                      <span 
                        key={i}
                        className={`inline-block ${i === slides[currentSlide].title.split(' ').length - 1 ? 'bg-gradient-to-r ' + slides[currentSlide].color + ' bg-clip-text text-transparent' : ''}`}
                        style={{
                          transform: `translateY(${Math.sin((Date.now() / 1000 + i) * 2) * 5}px)`,
                          transition: 'transform 0.3s ease'
                        }}
                      >
                        {word}{' '}
                      </span>
                    ))}
                  </h2>
                  <h3 className="text-2xl text-gray-700 mb-4 font-bold">
                    {slides[currentSlide].subtitle}
                  </h3>
                  <p className="text-xl text-gray-600 leading-relaxed">
                    {slides[currentSlide].description}
                  </p>
                </div>

                {/* Features Grid */}
                <div 
                  className="grid grid-cols-2 md:grid-cols-4 gap-4 animate-fade-in-up"
                  style={{ animationDelay: '0.6s' }}
                >
                  {[
                    { icon: Shield, label: '100% Sécurisé', color: 'text-green-500' },
                    { icon: Zap, label: 'Ultra Rapide', color: 'text-yellow-500' },
                    { icon: Users, label: '1000+ Clients', color: 'text-blue-500' },
                    { icon: TrendingUp, label: 'Top Qualité', color: 'text-purple-500' }
                  ].map((feature, i) => (
                    <div 
                      key={i}
                      className="bg-white/80 backdrop-blur-xl rounded-2xl p-4 border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 text-center"
                      style={{
                        transform: `translateY(${Math.sin((Date.now() / 1000 + i) * 1.5) * 3}px)`
                      }}
                    >
                      <feature.icon className={`w-6 h-6 ${feature.color} mx-auto mb-2`} />
                      <span className="text-sm font-semibold text-gray-800">{feature.label}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* CTA Buttons */}
              <div 
                className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start animate-fade-in-up"
                style={{ animationDelay: '0.8s' }}
              >
                <button
                  onClick={onGetStarted}
                  className="group bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-bold py-4 px-8 rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-2xl shadow-orange-500/25 hover:shadow-orange-500/40 flex items-center justify-center space-x-3"
                >
                  <span>Commencer maintenant</span>
                  <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
                </button>
                
                <button className="group bg-white/80 backdrop-blur-xl hover:bg-white text-gray-800 font-bold py-4 px-8 rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-lg border border-gray-200 flex items-center justify-center space-x-3">
                  <Smartphone className="w-5 h-5" />
                  <span>Découvrir l'app</span>
                </button>
              </div>
            </div>

            {/* Right Content - 3D Showcase */}
            <div className="relative">
              <div 
                className="relative w-full h-96 lg:h-[500px] perspective-1000"
                style={{
                  transform: `rotateY(${mousePosition.x * 0.1}deg) rotateX(${mousePosition.y * 0.05}deg)`
                }}
              >
                {/* 3D Product Showcase */}
                <div className="absolute inset-0 bg-gradient-to-br from-white to-gray-100 rounded-3xl shadow-2xl border border-gray-200 overflow-hidden transform-gpu">
                  <div className="absolute inset-0 bg-gradient-to-br from-orange-500/10 to-orange-600/10" />
                  
                  {/* Floating Product Cards */}
                  {[
                    { name: 'Porte Premium', price: '1200 DH', image: '🚪', delay: '0s' },
                    { name: 'Fenêtre Alu', price: '800 DH', image: '🪟', delay: '0.5s' },
                    { name: 'Menuiserie', price: '600 DH', image: '🔨', delay: '1s' }
                  ].map((product, i) => (
                    <div
                      key={i}
                      className="absolute bg-white rounded-2xl p-4 shadow-xl border border-gray-200 animate-bounce-gentle"
                      style={{
                        left: `${20 + i * 25}%`,
                        top: `${30 + i * 15}%`,
                        animationDelay: product.delay,
                        transform: `translateZ(${i * 20}px) rotateY(${i * 10}deg)`
                      }}
                    >
                      <div className="text-4xl mb-2">{product.image}</div>
                      <h4 className="font-bold text-gray-900 text-sm">{product.name}</h4>
                      <p className="text-orange-600 font-bold text-sm">{product.price}</p>
                    </div>
                  ))}

                  {/* Central Logo */}
                  <div 
                    className="absolute inset-0 flex items-center justify-center"
                    style={{
                      transform: `translateZ(50px) rotateY(${mousePosition.x * 0.2}deg)`
                    }}
                  >
                    <div className="w-32 h-32 bg-gradient-to-r from-orange-500 to-orange-600 rounded-full flex items-center justify-center shadow-2xl animate-pulse-slow">
                      <Door className="w-16 h-16 text-white" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>

        {/* Bottom Navigation */}
        <footer className="p-6">
          <div className="flex items-center justify-center space-x-8">
            {slides.map((_, i) => (
              <button
                key={i}
                onClick={() => setCurrentSlide(i)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  i === currentSlide 
                    ? 'bg-orange-500 scale-125' 
                    : 'bg-gray-300 hover:bg-gray-400'
                }`}
              />
            ))}
          </div>
          
          <div className="text-center mt-6">
            <p className="text-gray-600 text-sm">
              Rejoignez plus de <span className="font-bold text-orange-600">1000+ clients satisfaits</span>
            </p>
            <div className="flex items-center justify-center space-x-1 mt-2">
              {[...Array(5)].map((_, i) => (
                <Star key={i} className="w-4 h-4 text-orange-400 fill-current" />
              ))}
              <span className="text-sm text-gray-600 ml-2">4.9/5 - Excellent</span>
            </div>
          </div>
        </footer>
      </div>

      <style jsx>{`
        .perspective-1000 {
          perspective: 1000px;
        }
        
        .transform-gpu {
          transform-style: preserve-3d;
        }
        
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        
        @keyframes bounceGentle {
          0%, 100% {
            transform: translateY(0);
          }
          50% {
            transform: translateY(-10px);
          }
        }
        
        @keyframes pulse-slow {
          0%, 100% {
            opacity: 1;
            transform: scale(1);
          }
          50% {
            opacity: 0.8;
            transform: scale(1.05);
          }
        }
        
        .animate-fade-in-up {
          animation: fadeInUp 0.8s ease-out forwards;
          opacity: 0;
        }
        
        .animate-bounce-gentle {
          animation: bounceGentle 3s ease-in-out infinite;
        }
        
        .animate-pulse-slow {
          animation: pulse-slow 4s ease-in-out infinite;
        }
      `}</style>
    </div>
  );
};

export default WelcomePage;
