import React, { useState, useEffect } from 'react';
import { ChevronUp, ArrowRight } from 'lucide-react';

interface WelcomePageProps {
  onEnter: () => void;
}

const WelcomePage: React.FC<WelcomePageProps> = ({ onEnter }) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [showSwipeHint, setShowSwipeHint] = useState(false);

  useEffect(() => {
    // Animation d'entrée
    setTimeout(() => setIsLoaded(true), 500);
    // Afficher l'indication de glissement après 3 secondes
    setTimeout(() => setShowSwipeHint(true), 3000);
  }, []);

  const handleSwipeUp = (e: React.TouchEvent) => {
    const touch = e.touches[0];
    const startY = touch.clientY;

    const handleTouchMove = (moveEvent: TouchEvent) => {
      const currentY = moveEvent.touches[0].clientY;
      const deltaY = startY - currentY;

      if (deltaY > 100) { // Glissement vers le haut de 100px
        onEnter();
        document.removeEventListener('touchmove', handleTouchMove);
      }
    };

    document.addEventListener('touchmove', handleTouchMove);
    document.addEventListener('touchend', () => {
      document.removeEventListener('touchmove', handleTouchMove);
    }, { once: true });
  };

  return (
    <div
      className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-orange-100 flex items-center justify-center relative overflow-hidden"
      onTouchStart={handleSwipeUp}
      onClick={onEnter} // Pour desktop
    >
      {/* Particules flottantes en arrière-plan */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute w-2 h-2 bg-orange-200 rounded-full opacity-30 animate-float"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 5}s`,
              animationDuration: `${3 + Math.random() * 4}s`
            }}
          />
        ))}
      </div>

      {/* Cercles décoratifs */}
      <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-orange-200 to-orange-300 rounded-full opacity-20 blur-xl animate-pulse" />
      <div className="absolute bottom-32 right-16 w-24 h-24 bg-gradient-to-br from-orange-300 to-orange-400 rounded-full opacity-25 blur-lg animate-pulse" style={{ animationDelay: '1s' }} />
      <div className="absolute top-1/3 right-8 w-16 h-16 bg-gradient-to-br from-orange-100 to-orange-200 rounded-full opacity-30 blur-md animate-pulse" style={{ animationDelay: '2s' }} />

      {/* Contenu principal */}
      <div className="text-center z-10 px-8">
        {/* Logo 3D Container */}
        <div className={`relative mb-8 transform transition-all duration-1000 ${isLoaded ? 'scale-100 opacity-100 translate-y-0' : 'scale-75 opacity-0 translate-y-8'}`}>
          {/* Ombre du logo */}
          <div className="absolute inset-0 transform translate-y-4 translate-x-4 opacity-20">
            <div className="relative">
              {/* Porte 3D - Ombre */}
              <div className="relative mx-auto mb-6">
                <div className="w-24 h-32 bg-gray-400 rounded-r-xl transform perspective-1000 rotate-y-12 shadow-2xl" />
                <div className="absolute top-1/2 right-2 w-2 h-2 bg-gray-600 rounded-full transform -translate-y-1/2" />
              </div>
              {/* Texte DOORLY - Ombre */}
              <div className="text-4xl font-bold text-gray-400 tracking-wider">
                DOORLY
              </div>
            </div>
          </div>

          {/* Logo principal */}
          <div className="relative">
            {/* Porte 3D */}
            <div className="relative mx-auto mb-6 group">
              <div className="w-24 h-32 bg-gradient-to-br from-orange-200 via-orange-300 to-orange-400 rounded-r-xl transform perspective-1000 rotate-y-12 shadow-2xl transition-transform duration-500 group-hover:rotate-y-6">
                {/* Texture bois */}
                <div className="absolute inset-0 bg-gradient-to-b from-transparent via-white/10 to-transparent rounded-r-xl" />
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent rounded-r-xl" />

                {/* Poignée */}
                <div className="absolute top-1/2 right-2 w-2 h-2 bg-gradient-to-br from-amber-600 to-amber-800 rounded-full transform -translate-y-1/2 shadow-lg" />

                {/* Reflets */}
                <div className="absolute top-2 left-2 w-4 h-8 bg-white/20 rounded-full blur-sm" />
                <div className="absolute bottom-4 right-1 w-2 h-4 bg-white/10 rounded-full blur-sm" />
              </div>

              {/* Côté de la porte (effet 3D) */}
              <div className="absolute top-0 right-0 w-3 h-32 bg-gradient-to-b from-orange-400 to-orange-600 transform skew-y-12 origin-top shadow-xl" />
            </div>

            {/* Texte DOORLY avec effet 3D */}
            <div className="relative">
              <div className="text-4xl font-bold bg-gradient-to-r from-orange-600 via-orange-500 to-orange-700 bg-clip-text text-transparent tracking-wider transform perspective-1000 hover:scale-105 transition-transform duration-300">
                DOORLY
              </div>
              {/* Effet de profondeur sur le texte */}
              <div className="absolute inset-0 text-4xl font-bold text-orange-800/20 tracking-wider transform translate-x-1 translate-y-1 -z-10">
                DOORLY
              </div>
            </div>
          </div>
        </div>

        {/* Indication de glissement */}
        <div className={`transition-all duration-500 ${showSwipeHint ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
          <div className="flex flex-col items-center space-y-3 text-orange-600/70">
            <div className="animate-bounce">
              <ChevronUp className="w-6 h-6" />
            </div>
            <p className="text-sm font-medium">Glissez vers le haut pour continuer</p>
            <div className="hidden md:flex items-center space-x-2 text-xs">
              <span>ou cliquez</span>
              <ArrowRight className="w-4 h-4" />
            </div>
          </div>
        </div>
      </div>

      {/* Gradient overlay pour l'effet de profondeur */}
      <div className="absolute inset-0 bg-gradient-to-t from-white/10 via-transparent to-white/5 pointer-events-none" />
    </div>
  );
};

export default WelcomePage;
