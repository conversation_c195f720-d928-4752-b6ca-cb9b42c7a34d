import React from 'react';
import { LogIn, UserPlus } from 'lucide-react';
import <PERSON><PERSON><PERSON><PERSON> from './DoorlyLogo';

interface AuthChoicePageProps {
  onLogin: () => void;
  onRegister: () => void;
}

const AuthChoicePage: React.FC<AuthChoicePageProps> = ({ onLogin, onRegister }) => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-400 via-orange-500 to-orange-600 relative overflow-hidden perspective-5d">
      {/* Background Pattern 5D */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-20 left-10 w-32 h-32 bg-white rounded-full blur-xl animate-float-5d-ultra"></div>
        <div className="absolute top-40 right-20 w-24 h-24 bg-white rounded-full blur-lg animate-depth-field-blur"></div>
        <div className="absolute bottom-32 left-1/4 w-40 h-40 bg-white rounded-full blur-2xl animate-quantum-glow"></div>
        <div className="absolute bottom-20 right-10 w-28 h-28 bg-white rounded-full blur-xl animate-holographic-shift"></div>
      </div>

      {/* Floating Geometric Shapes 5D */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/4 left-8 w-8 h-8 bg-white/20 rounded-lg rotate-45 animate-float-5d-ultra transform-5d"></div>
        <div className="absolute top-1/3 right-12 w-6 h-6 bg-white/30 rounded-full animate-depth-field-blur transform-5d"></div>
        <div className="absolute bottom-1/3 left-16 w-10 h-10 bg-white/25 rounded-lg rotate-12 animate-quantum-glow transform-5d"></div>
        <div className="absolute top-2/3 right-1/4 w-4 h-4 bg-white/40 rounded-full animate-holographic-shift transform-5d"></div>
        <div className="absolute top-1/2 left-1/2 w-12 h-12 bg-white/15 rounded-full animate-glassmorphism-5d transform-5d"></div>
      </div>

      {/* Diagonal Lines Pattern */}
      <div className="absolute inset-0 opacity-5">
        {Array.from({ length: 20 }).map((_, i) => (
          <div
            key={i}
            className="absolute h-full w-0.5 bg-white transform rotate-45 animate-pulse"
            style={{
              left: `${i * 10}%`,
              animationDelay: `${i * 0.1}s`,
            }}
          />
        ))}
      </div>

      {/* Main Content */}
      <div className="relative z-10 flex flex-col items-center justify-center min-h-screen px-6">
        {/* Logo Doorly 5D */}
        <div className="text-center mb-16 animate-fade-in-up">
          <div className="relative w-24 h-32 mx-auto mb-6 animate-float-5d-ultra transform-5d perspective-5d">
            {/* Door Shadow 5D */}
            <div className="absolute top-3 left-3 w-full h-full bg-black/50 rounded-xl transform rotate-3 blur-xl animate-depth-field-blur"></div>
            
            {/* Main Door 5D */}
            <div className="relative w-full h-full bg-gradient-to-br from-amber-100 to-amber-200 rounded-xl shadow-2xl transform -rotate-2 border-3 border-amber-300 animate-quantum-glow">
              {/* Door Frame */}
              <div className="absolute inset-2 border-2 border-amber-400 rounded-lg animate-holographic-shift"></div>
              
              {/* Door Handle */}
              <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                <div className="w-3 h-3 bg-gradient-to-br from-yellow-600 to-yellow-800 rounded-full shadow-xl animate-quantum-glow"></div>
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-1.5 h-1.5 bg-yellow-900 rounded-full"></div>
              </div>
              
              {/* Wood Grain Effect 5D */}
              <div className="absolute inset-0 opacity-40 animate-holographic-shift">
                <div className="h-full w-full bg-gradient-to-r from-transparent via-amber-300 to-transparent"></div>
                <div className="absolute top-0 h-full w-full bg-gradient-to-b from-transparent via-amber-300 to-transparent opacity-60"></div>
              </div>
              
              {/* 5D Holographic Effect */}
              <div className="absolute inset-0 bg-gradient-to-br from-white/30 to-transparent rounded-xl animate-glassmorphism-5d"></div>
            </div>
            
            {/* 3D Effect Lines */}
            <div className="absolute -top-1 -right-1 w-full h-full border-2 border-amber-400 rounded-xl opacity-70 transform rotate-1 animate-depth-field-blur"></div>
          </div>
          
          <h1 className="text-4xl font-black text-white mb-3 animate-quantum-glow transform-5d">
            DOORLY
          </h1>
          <h2 className="text-2xl font-bold text-white mb-2">Bienvenue !</h2>
          <p className="text-white/80 text-lg">Choisissez votre action</p>
        </div>

        {/* Action Buttons */}
        <div className="space-y-6 w-full max-w-sm">
          {/* Login Button */}
          <button
            onClick={onLogin}
            className="w-full glass-5d-white px-8 py-4 rounded-2xl font-bold text-lg text-orange-600 hover:text-orange-700 transition-all duration-500 transform-5d hover:scale-105 hover:-translate-y-1 animate-quantum-glow shadow-2xl flex items-center justify-center"
          >
            <LogIn className="w-6 h-6 mr-3" />
            SE CONNECTER
          </button>

          {/* Register Button */}
          <button
            onClick={onRegister}
            className="w-full glass-5d-white px-8 py-4 rounded-2xl font-bold text-lg text-orange-600 hover:text-orange-700 transition-all duration-500 transform-5d hover:scale-105 hover:-translate-y-1 animate-quantum-glow shadow-2xl flex items-center justify-center"
          >
            <UserPlus className="w-6 h-6 mr-3" />
            S'IDENTIFIER
          </button>
        </div>
      </div>
    </div>
  );
};

export default AuthChoicePage;
